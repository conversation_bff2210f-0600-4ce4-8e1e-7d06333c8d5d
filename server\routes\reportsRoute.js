const authMiddleware = require("../middlewares/authMiddleware");
const Exam = require("../models/examModel");
const User = require("../models/userModel");
const Report = require("../models/reportModel");
const Subscription = require("../models/subscriptionModel");
const router = require("express").Router();

// add report

router.post("/add-report", authMiddleware, async (req, res) => {
  try {
    const newReport = new Report(req.body);
    await newReport.save();
    res.send({
      message: "Attempt added successfully",
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// get all reports

router.post("/get-all-reports", authMiddleware, async (req, res) => {
  try {
    const { examName, userName, page, limit } = req.body;

    // Fetch exams matching the name
    const exams = await Exam.find({
      name: {
        $regex: examName,
        $options: "i", // Case-insensitive matching
      },
    });

    const matchedExamIds = exams.map((exam) => exam._id);

    // Fetch users matching the name
    const users = await User.find({
      name: {
        $regex: userName,
        $options: "i",
      },
    });

    const matchedUserIds = users.map((user) => user._id);

    // Fetch reports with pagination
    const reports = await Report.find({
      exam: {
        $in: matchedExamIds,
      },
      user: {
        $in: matchedUserIds,
      },
    })
      .populate("exam")
      .populate("user")
      .sort({ createdAt: -1 }) // Sort by most recent
      .skip((page - 1) * limit) // Skip documents for previous pages
      .limit(parseInt(limit)); // Limit number of documents per page

    // Count total matching documents
    const totalReports = await Report.countDocuments({
      exam: {
        $in: matchedExamIds,
      },
      user: {
        $in: matchedUserIds,
      },
    });

    res.send({
      message: "Attempts fetched successfully",
      data: reports,
      success: true,
      pagination: {
        totalReports,
        currentPage: page,
        totalPages: Math.ceil(totalReports / limit),
      },
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});


// get all reports by user
router.post("/get-all-reports-by-user", authMiddleware, async (req, res) => {
  try {
    const reports = await Report.find({ user: req.body.userId })
      .populate("exam")
      .populate("user")
      .sort({ createdAt: -1 });
    res.send({
      message: "Attempts fetched successfully",
      data: reports,
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// get all reports for ranking
router.get("/get-all-reports-for-ranking", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    // Fetch the requesting user's details
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Determine match conditions based on user's level
    let matchConditions;
    if (user.level === "secondary") {
      // If viewing user is secondary, show only users who are secondary AND took secondary exams
      matchConditions = {
        $and: [
          {
            "userDetails.level": "secondary",
          },
          {
            "examDetails.level": "secondary"
          }
        ]
      };
    } else if (user.level === "advance") {
      // If viewing user is advance, show only users who are advance AND took advance exams
      matchConditions = {
        $and: [
          {
            "userDetails.level": "advance",
          },
          {
            "examDetails.level": "advance"
          }
        ]
      };
    } else {
      // For primary level, exclude any reports where either user or exam is secondary/advance
      matchConditions = {
        $and: [
          {
            "userDetails.level": { $nin: ["secondary", "advance"] }
          },
          {
            "examDetails.level": { $nin: ["secondary", "advance"] }
          }
        ]
      };
    }

    const distinctPassReportsCountPerUser = await Report.aggregate([
      // Stage 1: Lookup exam details
      {
        $lookup: {
          from: "exams",
          localField: "exam",
          foreignField: "_id",
          as: "examDetails"
        }
      },
      // Stage 2: Unwind exam details
      {
        $unwind: "$examDetails"
      },
      // Stage 3: Lookup user details
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails"
        }
      },
      // Stage 4: Unwind user details
      {
        $unwind: "$userDetails"
      },
      // Stage 5: Match conditions
      {
        $match: matchConditions
      },

      // Stage 6: Group by user and exam for distinct score & attempts
      {
        $group: {
          _id: { user: "$user", exam: "$exam" },
          maxScore: {
            $max: {
              $cond: [{ $eq: ["$result.verdict", "Pass"] }, "$result.score", 0]
            }
          },
          maxPoints: {
            $max: {
              $cond: [{ $ne: ["$result.points", null] }, "$result.points", 0]
            }
          },
          totalMarks: { $first: "$examDetails.totalMarks" },
          attempts: { $sum: 1 },
          userDetails: { $first: "$userDetails" }
        }
      },
      // Stage 7: Group by user to compute aggregates
      {
        $group: {
          _id: "$_id.user",
          totalMaxScores: { $sum: "$maxScore" },
          totalPoints: { $sum: "$maxPoints" },
          totalPossibleScores: { $sum: "$totalMarks" },
          retryCount: { $sum: { $subtract: ["$attempts", 1] } },
          quizzesTaken: { $sum: 1 },
          userDetails: { $first: "$userDetails" },
          passedExamsCount: {
            $sum: {
              $cond: [{ $gt: ["$maxScore", 0] }, 1, 0]
            }
          }

        }
      },
      // Stage 8: Project final output with rankingScore
      {
        $project: {
          userId: "$_id",
          userPhoto: "$userDetails.profileImage",
          userName: "$userDetails.name",
          userSchool: "$userDetails.school",
          userClass: "$userDetails.class",
          userLevel: "$userDetails.level",
          subscriptionStatus: "$userDetails.subscriptionStatus",
          subscriptionPlan: "$userDetails.subscriptionPlan",
          achievements: "$userDetails.achievements",
          totalPoints: 1,
          quizzesTaken: 1,
          passedExamsCount: 1,
          retryCount: 1,
          scoreRatio: {
            $cond: [
              { $gt: ["$totalPossibleScores", 0] },
              { $divide: ["$totalMaxScores", "$totalPossibleScores"] },
              0
            ]
          },
          rankingScore: {
            $add: [
              "$totalPoints",
              { $multiply: ["$passedExamsCount", 50] }, // Bonus for passing exams
              { $subtract: [0, { $multiply: ["$retryCount", 5] }] }, // Penalty for retries
              // Premium user bonus
              {
                $cond: [
                  { $in: ["$userDetails.subscriptionStatus", ["premium", "active"]] },
                  10, // 10 point bonus for premium users
                  0
                ]
              }
            ]
          },
          _id: 0
        }
      },
      // Stage 9: Sort by rankingScore then totalPoints then name
      {
        $sort: {
          rankingScore: -1,
          totalPoints: -1,
          userName: 1
        }
      }
    ]);

    const updatedResults = await Promise.all(
      distinctPassReportsCountPerUser.map(async (row) => {
        // Get user info to check if payment is required
        const User = require("../models/userModel");
        const user = await User.findById(row.userId);

        let subscriptionStatus = "expired";

        // If user doesn't require payment, they have free access
        if (user && !user.paymentRequired) {
          subscriptionStatus = "free";
        } else {
          // User requires payment, check subscription
          const subscription = await Subscription.findOne({ user: row.userId });

          if (subscription) {
            // Primary check: Use the subscription status field
            if (subscription.status === "active" && subscription.paymentStatus === "paid") {
              // Double-check the end date to ensure it's still valid
              if (subscription.endDate) {
                const endDate = new Date(subscription.endDate);
                const currentDate = new Date();

                if (endDate > currentDate) {
                  subscriptionStatus = "active";
                } else {
                  // Subscription has expired, update the status
                  subscription.status = "expired";
                  await subscription.save();
                  subscriptionStatus = "expired";
                }
              } else {
                // No end date but status is active - consider it active
                subscriptionStatus = "active";
              }
            } else if (subscription.status === "pending") {
              // Payment is still pending
              subscriptionStatus = "expired"; // Treat pending as expired for display
            } else {
              // Status is expired or failed
              subscriptionStatus = "expired";
            }
          }
        }

        return {
          ...row,
          subscriptionStatus
        };
      })
    );

    res.send({
      message: "Reports for all users fetched successfully",
      data: updatedResults,
      success: true
    });


  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false
    });
  }
});

module.exports = router;