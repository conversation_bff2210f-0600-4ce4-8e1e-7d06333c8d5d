{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { TbTrophy, TbUsers, TbSchool } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  console.log('Ranking component loaded');\n  const [rankingData, setRankingData] = useState([]);\n  const [userData, setUserData] = useState(null);\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\n  const [loading, setLoading] = useState(true);\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      console.log('Starting fetchReports...');\n      alert('fetchReports called!');\n      const response = await getAllReportsForRanking();\n      console.log('Ranking API Response:', response);\n      alert('API response received');\n      if (response.success) {\n        console.log('Raw ranking data:', response.data);\n        // Transform data for our new components\n        const transformedData = response.data.map((item, index) => ({\n          userId: item.userId,\n          _id: item.userId,\n          name: item.userName,\n          profilePicture: item.userPhoto || null,\n          subscriptionStatus: item.subscriptionStatus || 'free',\n          totalPoints: item.totalPoints || 0,\n          passedExamsCount: item.passedExamsCount || 0,\n          quizzesTaken: item.quizzesTaken || 0,\n          score: item.totalPoints || 0,\n          rank: index + 1,\n          userSchool: item.userSchool || 'Not Enrolled',\n          userClass: item.userClass || 'Not Enrolled'\n        }));\n        console.log('Transformed ranking data:', transformedData);\n        alert(`Received ${transformedData.length} users for ranking`);\n        setRankingData(transformedData);\n      } else {\n        console.log('API response failed:', response.message);\n        alert(`API failed: ${response.message}`);\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      console.log('Current user data:', response);\n      if (response.success) {\n        console.log('User isAdmin:', response.data.isAdmin);\n        if (response.data.isAdmin) {\n          console.log('User is admin, not fetching reports');\n          setIsAdmin(true);\n          dispatch(HideLoading());\n        } else {\n          console.log('User is not admin, fetching reports');\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchReports();\n          dispatch(HideLoading());\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n\n  // Temporarily disable admin check for debugging\n  // if (isAdmin) {\n  //     return (\n  //         <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n  //             <div className=\"text-center\">\n  //                 <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n  //                 <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Admin Access</h2>\n  //                 <p className=\"text-gray-600\">Ranking is only available for students</p>\n  //                 <p className=\"text-sm text-red-500 mt-2\">DEBUG: User is detected as admin</p>\n  //             </div>\n  //         </div>\n  //     );\n  // }\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading ranking data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"Student Leaderboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600\",\n          children: \"See how you rank against other students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-2 shadow-sm border border-gray-200 max-w-md mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${activeTab === \"overall\" ? 'bg-blue-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"overall\"),\n              children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Overall\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${activeTab === \"class\" ? 'bg-blue-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"class\"),\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: rankingData,\n          currentUserId: userData === null || userData === void 0 ? void 0 : userData._id,\n          layout: \"horizontal\",\n          size: \"medium\",\n          showSearch: true,\n          showFilters: true,\n          showStats: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"CQ5NosNnAJcL4CGyxydCkouMRgE=\", false, function () {\n  return [useDispatch];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "getAllReportsForRanking", "getUserInfo", "message", "useDispatch", "HideLoading", "ShowLoading", "UserRankingList", "TbTrophy", "TbUsers", "TbSchool", "jsxDEV", "_jsxDEV", "Ranking", "_s", "console", "log", "rankingData", "setRankingData", "userData", "setUserData", "isAdmin", "setIsAdmin", "activeTab", "setActiveTab", "loading", "setLoading", "dispatch", "fetchReports", "alert", "response", "success", "data", "transformedData", "map", "item", "index", "userId", "_id", "name", "userName", "profilePicture", "userPhoto", "subscriptionStatus", "totalPoints", "passedExamsCount", "quizzesTaken", "score", "rank", "userSchool", "userClass", "length", "error", "getUserData", "localStorage", "getItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "delay", "button", "whileHover", "scale", "whileTap", "onClick", "users", "currentUserId", "layout", "size", "showSearch", "showFilters", "showStats", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { TbTrophy, TbUsers, TbSchool } from \"react-icons/tb\";\r\n\r\nconst Ranking = () => {\r\n    console.log('Ranking component loaded');\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [userData, setUserData] = useState(null);\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            console.log('Starting fetchReports...');\r\n            alert('fetchReports called!');\r\n            const response = await getAllReportsForRanking();\r\n            console.log('Ranking API Response:', response);\r\n            alert('API response received');\r\n            if (response.success) {\r\n                console.log('Raw ranking data:', response.data);\r\n                // Transform data for our new components\r\n                const transformedData = response.data.map((item, index) => ({\r\n                    userId: item.userId,\r\n                    _id: item.userId,\r\n                    name: item.userName,\r\n                    profilePicture: item.userPhoto || null,\r\n                    subscriptionStatus: item.subscriptionStatus || 'free',\r\n                    totalPoints: item.totalPoints || 0,\r\n                    passedExamsCount: item.passedExamsCount || 0,\r\n                    quizzesTaken: item.quizzesTaken || 0,\r\n                    score: item.totalPoints || 0,\r\n                    rank: index + 1,\r\n                    userSchool: item.userSchool || 'Not Enrolled',\r\n                    userClass: item.userClass || 'Not Enrolled'\r\n                }));\r\n                console.log('Transformed ranking data:', transformedData);\r\n                alert(`Received ${transformedData.length} users for ranking`);\r\n                setRankingData(transformedData);\r\n            } else {\r\n                console.log('API response failed:', response.message);\r\n                alert(`API failed: ${response.message}`);\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }\r\n\r\n\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            console.log('Current user data:', response);\r\n            if (response.success) {\r\n                console.log('User isAdmin:', response.data.isAdmin);\r\n                if (response.data.isAdmin) {\r\n                    console.log('User is admin, not fetching reports');\r\n                    setIsAdmin(true);\r\n                    dispatch(HideLoading());\r\n                } else {\r\n                    console.log('User is not admin, fetching reports');\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    // Temporarily disable admin check for debugging\r\n    // if (isAdmin) {\r\n    //     return (\r\n    //         <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n    //             <div className=\"text-center\">\r\n    //                 <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n    //                 <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Admin Access</h2>\r\n    //                 <p className=\"text-gray-600\">Ranking is only available for students</p>\r\n    //                 <p className=\"text-sm text-red-500 mt-2\">DEBUG: User is detected as admin</p>\r\n    //             </div>\r\n    //         </div>\r\n    //     );\r\n    // }\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n                    <p className=\"text-gray-600\">Loading ranking data...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50\">\r\n            <div className=\"container mx-auto px-4 py-8\">\r\n                {/* Header */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center mb-8\"\r\n                >\r\n                    <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\">\r\n                        <TbTrophy className=\"w-8 h-8 text-white\" />\r\n                    </div>\r\n                    <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">Student Leaderboard</h1>\r\n                    <p className=\"text-xl text-gray-600\">\r\n                        See how you rank against other students\r\n                    </p>\r\n                </motion.div>\r\n\r\n                {/* Tabs for Overall vs Class Ranking */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.2 }}\r\n                    className=\"mb-8\"\r\n                >\r\n                    <div className=\"bg-white rounded-xl p-2 shadow-sm border border-gray-200 max-w-md mx-auto\">\r\n                        <div className=\"flex gap-2\">\r\n                            <motion.button\r\n                                whileHover={{ scale: 1.02 }}\r\n                                whileTap={{ scale: 0.98 }}\r\n                                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                    activeTab === \"overall\"\r\n                                        ? 'bg-blue-600 text-white shadow-md'\r\n                                        : 'text-gray-600 hover:bg-gray-100'\r\n                                }`}\r\n                                onClick={() => setActiveTab(\"overall\")}\r\n                            >\r\n                                <TbUsers className=\"w-5 h-5\" />\r\n                                <span>Overall</span>\r\n                            </motion.button>\r\n                            <motion.button\r\n                                whileHover={{ scale: 1.02 }}\r\n                                whileTap={{ scale: 0.98 }}\r\n                                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                    activeTab === \"class\"\r\n                                        ? 'bg-blue-600 text-white shadow-md'\r\n                                        : 'text-gray-600 hover:bg-gray-100'\r\n                                }`}\r\n                                onClick={() => setActiveTab(\"class\")}\r\n                            >\r\n                                <TbSchool className=\"w-5 h-5\" />\r\n                                <span>Class</span>\r\n                            </motion.button>\r\n                        </div>\r\n                    </div>\r\n                </motion.div>\r\n\r\n                {/* Modern Ranking List */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3 }}\r\n                >\r\n                    <UserRankingList\r\n                        users={rankingData}\r\n                        currentUserId={userData?._id}\r\n                        layout=\"horizontal\"\r\n                        size=\"medium\"\r\n                        showSearch={true}\r\n                        showFilters={true}\r\n                        showStats={true}\r\n                    />\r\n                </motion.div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM4B,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAMwB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACAb,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvCa,KAAK,CAAC,sBAAsB,CAAC;MAC7B,MAAMC,QAAQ,GAAG,MAAM7B,uBAAuB,CAAC,CAAC;MAChDc,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEc,QAAQ,CAAC;MAC9CD,KAAK,CAAC,uBAAuB,CAAC;MAC9B,IAAIC,QAAQ,CAACC,OAAO,EAAE;QAClBhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEc,QAAQ,CAACE,IAAI,CAAC;QAC/C;QACA,MAAMC,eAAe,GAAGH,QAAQ,CAACE,IAAI,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UACxDC,MAAM,EAAEF,IAAI,CAACE,MAAM;UACnBC,GAAG,EAAEH,IAAI,CAACE,MAAM;UAChBE,IAAI,EAAEJ,IAAI,CAACK,QAAQ;UACnBC,cAAc,EAAEN,IAAI,CAACO,SAAS,IAAI,IAAI;UACtCC,kBAAkB,EAAER,IAAI,CAACQ,kBAAkB,IAAI,MAAM;UACrDC,WAAW,EAAET,IAAI,CAACS,WAAW,IAAI,CAAC;UAClCC,gBAAgB,EAAEV,IAAI,CAACU,gBAAgB,IAAI,CAAC;UAC5CC,YAAY,EAAEX,IAAI,CAACW,YAAY,IAAI,CAAC;UACpCC,KAAK,EAAEZ,IAAI,CAACS,WAAW,IAAI,CAAC;UAC5BI,IAAI,EAAEZ,KAAK,GAAG,CAAC;UACfa,UAAU,EAAEd,IAAI,CAACc,UAAU,IAAI,cAAc;UAC7CC,SAAS,EAAEf,IAAI,CAACe,SAAS,IAAI;QACjC,CAAC,CAAC,CAAC;QACHnC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiB,eAAe,CAAC;QACzDJ,KAAK,CAAE,YAAWI,eAAe,CAACkB,MAAO,oBAAmB,CAAC;QAC7DjC,cAAc,CAACe,eAAe,CAAC;MACnC,CAAC,MAAM;QACHlB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEc,QAAQ,CAAC3B,OAAO,CAAC;QACrD0B,KAAK,CAAE,eAAcC,QAAQ,CAAC3B,OAAQ,EAAC,CAAC;QACxCA,OAAO,CAACiD,KAAK,CAACtB,QAAQ,CAAC3B,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACZjD,OAAO,CAACiD,KAAK,CAACA,KAAK,CAACjD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNuB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAKD,MAAM2B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMvB,QAAQ,GAAG,MAAM5B,WAAW,CAAC,CAAC;MACpCa,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEc,QAAQ,CAAC;MAC3C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QAClBhB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEc,QAAQ,CAACE,IAAI,CAACX,OAAO,CAAC;QACnD,IAAIS,QAAQ,CAACE,IAAI,CAACX,OAAO,EAAE;UACvBN,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;UAClDM,UAAU,CAAC,IAAI,CAAC;UAChBK,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QAC3B,CAAC,MAAM;UACHU,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;UAClDM,UAAU,CAAC,KAAK,CAAC;UACjBF,WAAW,CAACU,QAAQ,CAACE,IAAI,CAAC;UAC1B,MAAMJ,YAAY,CAAC,CAAC;UACpBD,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QAC3B;MACJ,CAAC,MAAM;QACHF,OAAO,CAACiD,KAAK,CAACtB,QAAQ,CAAC3B,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACZjD,OAAO,CAACiD,KAAK,CAACA,KAAK,CAACjD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDL,SAAS,CAAC,MAAM;IACZ,IAAIwD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/B5B,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAAC;MACvB+C,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAI5B,OAAO,EAAE;IACT,oBACIb,OAAA;MAAK4C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE7C,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB7C,OAAA;UAAK4C,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGjD,OAAA;UAAG4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIjD,OAAA;IAAK4C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACpC7C,OAAA;MAAK4C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAExC7C,OAAA,CAACZ,MAAM,CAAC8D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BT,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE5B7C,OAAA;UAAK4C,SAAS,EAAC,oHAAoH;UAAAC,QAAA,eAC/H7C,OAAA,CAACJ,QAAQ;YAACgD,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNjD,OAAA;UAAI4C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EjD,OAAA;UAAG4C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbjD,OAAA,CAACZ,MAAM,CAAC8D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhB7C,OAAA;UAAK4C,SAAS,EAAC,2EAA2E;UAAAC,QAAA,eACtF7C,OAAA;YAAK4C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB7C,OAAA,CAACZ,MAAM,CAACqE,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,kHACRjC,SAAS,KAAK,SAAS,GACjB,kCAAkC,GAClC,iCACT,EAAE;cACHkD,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAAC,SAAS,CAAE;cAAAiC,QAAA,gBAEvC7C,OAAA,CAACH,OAAO;gBAAC+C,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BjD,OAAA;gBAAA6C,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAChBjD,OAAA,CAACZ,MAAM,CAACqE,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,kHACRjC,SAAS,KAAK,OAAO,GACf,kCAAkC,GAClC,iCACT,EAAE;cACHkD,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAAC,OAAO,CAAE;cAAAiC,QAAA,gBAErC7C,OAAA,CAACF,QAAQ;gBAAC8C,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCjD,OAAA;gBAAA6C,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGbjD,OAAA,CAACZ,MAAM,CAAC8D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,eAE3B7C,OAAA,CAACL,eAAe;UACZmE,KAAK,EAAEzD,WAAY;UACnB0D,aAAa,EAAExD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmB,GAAI;UAC7BsC,MAAM,EAAC,YAAY;UACnBC,IAAI,EAAC,QAAQ;UACbC,UAAU,EAAE,IAAK;UACjBC,WAAW,EAAE,IAAK;UAClBC,SAAS,EAAE;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC/C,EAAA,CAzLID,OAAO;EAAA,QAQQT,WAAW;AAAA;AAAA6E,EAAA,GAR1BpE,OAAO;AA2Lb,eAAeA,OAAO;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}