import React from 'react';
import { motion } from 'framer-motion';
import { TbTrophy, TbMedal, TbCrown, TbStar, TbUsers, TbFlame, TbTarget } from 'react-icons/tb';
import { AchievementList } from './AchievementBadge';
import './UserRankingCard.css';

const UserRankingCard = ({ 
    user, 
    rank, 
    isCurrentUser = false, 
    layout = 'horizontal', // 'horizontal' or 'vertical'
    size = 'medium', // 'small', 'medium', 'large'
    showStats = true,
    className = ''
}) => {
    // Size configurations
    const sizeConfig = {
        small: {
            avatar: 'w-10 h-10',
            text: 'text-sm',
            subtext: 'text-xs',
            padding: 'p-3',
            spacing: 'space-x-3'
        },
        medium: {
            avatar: 'w-12 h-12',
            text: 'text-base',
            subtext: 'text-sm',
            padding: 'p-4',
            spacing: 'space-x-4'
        },
        large: {
            avatar: 'w-16 h-16',
            text: 'text-lg',
            subtext: 'text-base',
            padding: 'p-5',
            spacing: 'space-x-5'
        }
    };

    const config = sizeConfig[size];

    // Get subscription status styling
    const getSubscriptionStyling = () => {
        if (user.subscriptionStatus === 'active' || user.subscriptionStatus === 'premium') {
            return {
                avatarClass: 'avatar-premium premium-glow',
                badge: 'status-premium',
                glow: 'shadow-lg shadow-yellow-400/50'
            };
        } else if (user.subscriptionStatus === 'free') {
            return {
                avatarClass: 'avatar-free',
                badge: 'status-free',
                glow: 'shadow-md shadow-blue-400/30'
            };
        } else {
            return {
                avatarClass: 'avatar-expired',
                badge: 'status-expired',
                glow: 'shadow-sm'
            };
        }
    };

    const styling = getSubscriptionStyling();

    // Get rank icon and color
    const getRankDisplay = () => {
        if (rank === 1) {
            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };
        } else if (rank === 2) {
            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };
        } else if (rank === 3) {
            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };
        } else if (rank <= 10) {
            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };
        } else {
            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };
        }
    };

    const rankDisplay = getRankDisplay();
    const RankIcon = rankDisplay.icon;

    // Animation variants
    const cardVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { 
            opacity: 1, 
            y: 0,
            transition: { duration: 0.3 }
        },
        hover: { 
            scale: 1.02,
            transition: { duration: 0.2 }
        }
    };

    const avatarVariants = {
        hover: { 
            scale: 1.1,
            transition: { duration: 0.2 }
        }
    };

    // Avatar wrapper with subscription styling
    const StyledAvatar = ({ children }) => {
        return (
            <div className={`${config.avatar} ${styling.avatarClass} ${styling.glow}`}>
                {children}
            </div>
        );
    };

    if (layout === 'vertical') {
        return (
            <motion.div
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                className={`
                    ranking-card flex flex-col items-center text-center ${config.padding}
                    bg-white rounded-xl border border-gray-200
                    ${isCurrentUser ? 'current-user-card' : ''}
                    ${className}
                `}
            >
                {/* Rank Badge */}
                <div className={`
                    rank-badge flex items-center justify-center w-8 h-8 rounded-full mb-3
                    ${rankDisplay.bg} ${rankDisplay.color}
                `}>
                    {RankIcon ? (
                        <RankIcon className="w-4 h-4" />
                    ) : (
                        <span className="text-xs font-bold">#{rank}</span>
                    )}
                </div>

                {/* Avatar */}
                <motion.div variants={avatarVariants} whileHover="hover" className="mb-3">
                    <StyledAvatar>
                        <img
                            src={user.profilePicture || '/default-avatar.png'}
                            alt={user.name}
                            className="w-full h-full rounded-full object-cover bg-white"
                        />
                    </StyledAvatar>
                </motion.div>

                {/* User Info */}
                <div className="space-y-1">
                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>
                        {user.name}
                    </h3>
                    <p className={`${config.subtext} text-gray-500`}>
                        {user.totalPoints || 0} pts
                    </p>

                    {/* Enhanced Stats */}
                    {user.averageScore && (
                        <p className={`${config.subtext} text-gray-500`}>
                            Avg: {user.averageScore}%
                        </p>
                    )}

                    {user.currentStreak > 0 && (
                        <div className="flex items-center space-x-1">
                            <TbFlame className="w-3 h-3 text-orange-500" />
                            <span className={`${config.subtext} text-orange-600 font-medium`}>
                                {user.currentStreak}
                            </span>
                        </div>
                    )}

                    {/* Achievements */}
                    {user.achievements && user.achievements.length > 0 && (
                        <AchievementList
                            achievements={user.achievements}
                            maxDisplay={3}
                            size="small"
                            layout="horizontal"
                        />
                    )}

                    {/* Subscription Badge */}
                    <span className={`
                        inline-block px-2 py-1 rounded-full text-xs font-medium
                        ${styling.badge}
                    `}>
                        {user.subscriptionStatus === 'active' ? 'Premium' :
                         user.subscriptionStatus === 'free' ? 'Free' : 'Expired'}
                    </span>
                </div>
            </motion.div>
        );
    }

    // Horizontal layout (default)
    return (
        <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            className={`
                ranking-card flex items-center ${config.spacing} ${config.padding}
                bg-white rounded-xl border border-gray-200
                ${isCurrentUser ? 'current-user-card' : ''}
                ${className}
            `}
        >
            {/* Rank */}
            <div className={`
                rank-badge flex items-center justify-center w-10 h-10 rounded-full flex-shrink-0
                ${rankDisplay.bg} ${rankDisplay.color}
            `}>
                {RankIcon ? (
                    <RankIcon className="w-5 h-5" />
                ) : (
                    <span className="text-sm font-bold">#{rank}</span>
                )}
            </div>

            {/* Avatar */}
            <motion.div variants={avatarVariants} whileHover="hover" className="flex-shrink-0">
                <StyledAvatar>
                    <img
                        src={user.profilePicture || '/default-avatar.png'}
                        alt={user.name}
                        className="w-full h-full rounded-full object-cover bg-white"
                    />
                </StyledAvatar>
            </motion.div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>
                        {user.name}
                    </h3>
                    <span className={`
                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0
                        ${styling.badge}
                    `}>
                        {user.subscriptionStatus === 'active' ? 'Premium' :
                         user.subscriptionStatus === 'free' ? 'Free' : 'Expired'}
                    </span>
                </div>
                
                {showStats && (
                    <div className="flex items-center space-x-4">
                        <span className={`${config.subtext} text-gray-600 font-medium`}>
                            {user.totalPoints || 0} points
                        </span>
                        {user.passedExamsCount !== undefined && (
                            <span className={`${config.subtext} text-green-600`}>
                                {user.passedExamsCount} passed
                            </span>
                        )}
                        {user.quizzesTaken !== undefined && (
                            <span className={`${config.subtext} text-blue-600`}>
                                {user.quizzesTaken} quizzes
                            </span>
                        )}
                        {user.averageScore && (
                            <span className={`${config.subtext} text-gray-600`}>
                                {user.averageScore}% avg
                            </span>
                        )}
                        {user.currentStreak > 0 && (
                            <div className="flex items-center space-x-1">
                                <TbFlame className="w-3 h-3 text-orange-500" />
                                <span className={`${config.subtext} text-orange-600 font-medium`}>
                                    {user.currentStreak}
                                </span>
                            </div>
                        )}
                    </div>
                )}

                {/* Achievements for horizontal layout */}
                {user.achievements && user.achievements.length > 0 && (
                    <div className="mt-2">
                        <AchievementList
                            achievements={user.achievements}
                            maxDisplay={5}
                            size="small"
                            layout="horizontal"
                        />
                    </div>
                )}
            </div>

            {/* Score */}
            <div className="text-right flex-shrink-0">
                <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>
                    {user.score || user.totalPoints || 0}
                </div>
                <div className={`${config.subtext} text-gray-500`}>score</div>
            </div>
        </motion.div>
    );
};

export default UserRankingCard;
