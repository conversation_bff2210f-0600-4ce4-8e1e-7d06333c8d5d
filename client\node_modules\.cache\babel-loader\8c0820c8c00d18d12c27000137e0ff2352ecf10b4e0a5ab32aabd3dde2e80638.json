{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSearch, Tb<PERSON><PERSON>er, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = ''\n}) => {\n  _s();\n  const [showFindMe, setShowFindMe] = useState(false);\n  const currentUserRef = useRef(null);\n\n  // Filter and sort users\n  const filteredUsers = users.filter(user => {\n    // Add null checks for user properties\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || '';\n    const userSubscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || 'free';\n    const matchesSearch = userName.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterStatus === 'all' || filterStatus === 'premium' && (userSubscriptionStatus === 'active' || userSubscriptionStatus === 'premium') || filterStatus === 'free' && userSubscriptionStatus === 'free' || filterStatus === 'expired' && userSubscriptionStatus === 'expired';\n    return matchesSearch && matchesFilter;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'points':\n        return (b.totalPoints || 0) - (a.totalPoints || 0);\n      case 'name':\n        const nameA = (a === null || a === void 0 ? void 0 : a.name) || '';\n        const nameB = (b === null || b === void 0 ? void 0 : b.name) || '';\n        return nameA.localeCompare(nameB);\n      case 'rank':\n      default:\n        return (a.rank || 0) - (b.rank || 0);\n    }\n  });\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n    }\n  };\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n      case 'horizontal':\n      default:\n        return 'space-y-3';\n    }\n  };\n\n  // Container animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Stats summary\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium').length;\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.totalPoints || 0)) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-6 h-6 text-yellow-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Leaderboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 25\n        }, this), currentUserId && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: scrollToCurrentUser,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbUser, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Find Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-5 h-5 text-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: topScore\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 17\n    }, this), (showSearch || showFilters) && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-white rounded-xl p-4 border border-gray-200 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [showSearch && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search users...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 29\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterStatus,\n            onChange: e => setFilterStatus(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"premium\",\n              children: \"Premium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"free\",\n              children: \"Free\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"expired\",\n              children: \"Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"rank\",\n              children: \"By Rank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"points\",\n              children: \"By Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name\",\n              children: \"By Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      className: getLayoutClasses(),\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: filteredUsers.map((user, index) => {\n          const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n          const rank = index + 1;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            ref: isCurrentUser ? currentUserRef : null,\n            layout: true,\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: user,\n              rank: rank,\n              isCurrentUser: isCurrentUser,\n              layout: layout,\n              size: size,\n              showStats: showStats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 13\n    }, this), filteredUsers.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: searchTerm || filterStatus !== 'all' ? 'Try adjusting your search or filters' : 'No users available to display'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 17\n    }, this), currentUserId && filteredUsers.length > 10 && /*#__PURE__*/_jsxDEV(motion.button, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"Vw4fDE503ttXKNsyOIuzzP0EQSQ=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "TbSearch", "Tb<PERSON><PERSON>er", "TbUser", "TbUsers", "TbTrophy", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "_s", "showFindMe", "setShowFindMe", "currentUserRef", "filteredUsers", "filter", "user", "userName", "name", "userSubscriptionStatus", "subscriptionStatus", "matchesSearch", "toLowerCase", "includes", "searchTerm", "matchesFilter", "filterStatus", "sort", "a", "b", "sortBy", "totalPoints", "nameA", "nameB", "localeCompare", "rank", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "getLayoutClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "totalUsers", "length", "premiumUsers", "u", "topScore", "Math", "max", "map", "children", "div", "initial", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "onClick", "showSearch", "showFilters", "type", "placeholder", "value", "onChange", "e", "setSearchTerm", "target", "setFilterStatus", "setSortBy", "variants", "index", "isCurrentUser", "userId", "_id", "ref", "exit", "duration", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSearch, TbFilter, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = ''\n}) => {\n    const [showFindMe, setShowFindMe] = useState(false);\n    const currentUserRef = useRef(null);\n\n    // Filter and sort users\n    const filteredUsers = users\n        .filter(user => {\n            // Add null checks for user properties\n            const userName = user?.name || '';\n            const userSubscriptionStatus = user?.subscriptionStatus || 'free';\n\n            const matchesSearch = userName.toLowerCase().includes(searchTerm.toLowerCase());\n            const matchesFilter = filterStatus === 'all' ||\n                (filterStatus === 'premium' && (userSubscriptionStatus === 'active' || userSubscriptionStatus === 'premium')) ||\n                (filterStatus === 'free' && userSubscriptionStatus === 'free') ||\n                (filterStatus === 'expired' && userSubscriptionStatus === 'expired');\n            return matchesSearch && matchesFilter;\n        })\n        .sort((a, b) => {\n            switch (sortBy) {\n                case 'points':\n                    return (b.totalPoints || 0) - (a.totalPoints || 0);\n                case 'name':\n                    const nameA = a?.name || '';\n                    const nameB = b?.name || '';\n                    return nameA.localeCompare(nameB);\n                case 'rank':\n                default:\n                    return (a.rank || 0) - (b.rank || 0);\n            }\n        });\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (currentUserRef.current) {\n            currentUserRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n        }\n    };\n\n    // Get layout classes\n    const getLayoutClasses = () => {\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n            case 'horizontal':\n            default:\n                return 'space-y-3';\n        }\n    };\n\n    // Container animation variants\n    const containerVariants = {\n        hidden: { opacity: 0 },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n\n    // Stats summary\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium').length;\n    const topScore = users.length > 0 ? Math.max(...users.map(u => u.totalPoints || 0)) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\"\n                >\n                    <div className=\"flex items-center justify-between mb-4\">\n                        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">\n                            <TbTrophy className=\"w-6 h-6 text-yellow-500\" />\n                            <span>Leaderboard</span>\n                        </h2>\n                        \n                        {currentUserId && (\n                            <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={scrollToCurrentUser}\n                                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\"\n                            >\n                                <TbUser className=\"w-4 h-4\" />\n                                <span>Find Me</span>\n                            </motion.button>\n                        )}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUsers className=\"w-5 h-5 text-blue-500\" />\n                                <span className=\"text-sm text-gray-600\">Total Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{totalUsers}</div>\n                        </div>\n                        \n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbTrophy className=\"w-5 h-5 text-yellow-500\" />\n                                <span className=\"text-sm text-gray-600\">Premium Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{premiumUsers}</div>\n                        </div>\n                        \n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUser className=\"w-5 h-5 text-green-500\" />\n                                <span className=\"text-sm text-gray-600\">Top Score</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{topScore}</div>\n                        </div>\n                    </div>\n                </motion.div>\n            )}\n\n            {/* Search and Filters */}\n            {(showSearch || showFilters) && (\n                <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-white rounded-xl p-4 border border-gray-200 shadow-sm\"\n                >\n                    <div className=\"flex flex-col sm:flex-row gap-4\">\n                        {/* Search */}\n                        {showSearch && (\n                            <div className=\"flex-1\">\n                                <div className=\"relative\">\n                                    <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                                    <input\n                                        type=\"text\"\n                                        placeholder=\"Search users...\"\n                                        value={searchTerm}\n                                        onChange={(e) => setSearchTerm(e.target.value)}\n                                        className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    />\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Filters */}\n                        {showFilters && (\n                            <div className=\"flex gap-2\">\n                                <select\n                                    value={filterStatus}\n                                    onChange={(e) => setFilterStatus(e.target.value)}\n                                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                >\n                                    <option value=\"all\">All Users</option>\n                                    <option value=\"premium\">Premium</option>\n                                    <option value=\"free\">Free</option>\n                                    <option value=\"expired\">Expired</option>\n                                </select>\n\n                                <select\n                                    value={sortBy}\n                                    onChange={(e) => setSortBy(e.target.value)}\n                                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                >\n                                    <option value=\"rank\">By Rank</option>\n                                    <option value=\"points\">By Points</option>\n                                    <option value=\"name\">By Name</option>\n                                </select>\n                            </div>\n                        )}\n                    </div>\n                </motion.div>\n            )}\n\n            {/* User List */}\n            <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className={getLayoutClasses()}\n            >\n                <AnimatePresence>\n                    {filteredUsers.map((user, index) => {\n                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                        const rank = index + 1;\n                        \n                        return (\n                            <motion.div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? currentUserRef : null}\n                                layout\n                                initial={{ opacity: 0, scale: 0.9 }}\n                                animate={{ opacity: 1, scale: 1 }}\n                                exit={{ opacity: 0, scale: 0.9 }}\n                                transition={{ duration: 0.2 }}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </motion.div>\n                        );\n                    })}\n                </AnimatePresence>\n            </motion.div>\n\n            {/* Empty State */}\n            {filteredUsers.length === 0 && (\n                <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    className=\"text-center py-12\"\n                >\n                    <TbUsers className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        {searchTerm || filterStatus !== 'all' \n                            ? 'Try adjusting your search or filters'\n                            : 'No users available to display'\n                        }\n                    </p>\n                </motion.div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && filteredUsers.length > 10 && (\n                <motion.button\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </motion.button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9E,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMsB,cAAc,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMsB,aAAa,GAAGV,KAAK,CACtBW,MAAM,CAACC,IAAI,IAAI;IACZ;IACA,MAAMC,QAAQ,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,KAAI,EAAE;IACjC,MAAMC,sBAAsB,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,kBAAkB,KAAI,MAAM;IAEjE,MAAMC,aAAa,GAAGJ,QAAQ,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACC,UAAU,CAACF,WAAW,CAAC,CAAC,CAAC;IAC/E,MAAMG,aAAa,GAAGC,YAAY,KAAK,KAAK,IACvCA,YAAY,KAAK,SAAS,KAAKP,sBAAsB,KAAK,QAAQ,IAAIA,sBAAsB,KAAK,SAAS,CAAE,IAC5GO,YAAY,KAAK,MAAM,IAAIP,sBAAsB,KAAK,MAAO,IAC7DO,YAAY,KAAK,SAAS,IAAIP,sBAAsB,KAAK,SAAU;IACxE,OAAOE,aAAa,IAAII,aAAa;EACzC,CAAC,CAAC,CACDE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACZ,QAAQC,MAAM;MACV,KAAK,QAAQ;QACT,OAAO,CAACD,CAAC,CAACE,WAAW,IAAI,CAAC,KAAKH,CAAC,CAACG,WAAW,IAAI,CAAC,CAAC;MACtD,KAAK,MAAM;QACP,MAAMC,KAAK,GAAG,CAAAJ,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEV,IAAI,KAAI,EAAE;QAC3B,MAAMe,KAAK,GAAG,CAAAJ,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEX,IAAI,KAAI,EAAE;QAC3B,OAAOc,KAAK,CAACE,aAAa,CAACD,KAAK,CAAC;MACrC,KAAK,MAAM;MACX;QACI,OAAO,CAACL,CAAC,CAACO,IAAI,IAAI,CAAC,KAAKN,CAAC,CAACM,IAAI,IAAI,CAAC,CAAC;IAC5C;EACJ,CAAC,CAAC;;EAEN;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIvB,cAAc,CAACwB,OAAO,EAAE;MACxBxB,cAAc,CAACwB,OAAO,CAACC,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQnC,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAED;EACA,MAAMoC,iBAAiB,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACRC,eAAe,EAAE;MACrB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG5C,KAAK,CAAC6C,MAAM;EAC/B,MAAMC,YAAY,GAAG9C,KAAK,CAACW,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAAC/B,kBAAkB,KAAK,QAAQ,IAAI+B,CAAC,CAAC/B,kBAAkB,KAAK,SAAS,CAAC,CAAC6B,MAAM;EACtH,MAAMG,QAAQ,GAAGhD,KAAK,CAAC6C,MAAM,GAAG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAGlD,KAAK,CAACmD,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACpB,WAAW,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EAEvF,oBACI7B,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAA+C,QAAA,GAEpChD,SAAS,iBACNN,OAAA,CAACT,MAAM,CAACgE,GAAG;MACPC,OAAO,EAAE;QAAEd,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEhB,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAC9BlD,SAAS,EAAC,kFAAkF;MAAA+C,QAAA,gBAE5FtD,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAA+C,QAAA,gBACnDtD,OAAA;UAAIO,SAAS,EAAC,8DAA8D;UAAA+C,QAAA,gBACxEtD,OAAA,CAACH,QAAQ;YAACU,SAAS,EAAC;UAAyB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD9D,OAAA;YAAAsD,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EAEJ3D,aAAa,iBACVH,OAAA,CAACT,MAAM,CAACwE,MAAM;UACVC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BE,OAAO,EAAEjC,mBAAoB;UAC7B3B,SAAS,EAAC,sIAAsI;UAAA+C,QAAA,gBAEhJtD,OAAA,CAACL,MAAM;YAACY,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9B9D,OAAA;YAAAsD,QAAA,EAAM;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEN9D,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAA+C,QAAA,gBAClDtD,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA+C,QAAA,gBAC3DtD,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA+C,QAAA,gBACxCtD,OAAA,CAACJ,OAAO;cAACW,SAAS,EAAC;YAAuB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C9D,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA+C,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN9D,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA+C,QAAA,EAAER;UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eAEN9D,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA+C,QAAA,gBAC3DtD,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA+C,QAAA,gBACxCtD,OAAA,CAACH,QAAQ;cAACU,SAAS,EAAC;YAAyB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD9D,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA+C,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACN9D,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA+C,QAAA,EAAEN;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAEN9D,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA+C,QAAA,gBAC3DtD,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA+C,QAAA,gBACxCtD,OAAA,CAACL,MAAM;cAACY,SAAS,EAAC;YAAwB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C9D,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA+C,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACN9D,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA+C,QAAA,EAAEJ;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,EAGA,CAACM,UAAU,IAAIC,WAAW,kBACvBrE,OAAA,CAACT,MAAM,CAACgE,GAAG;MACPC,OAAO,EAAE;QAAEd,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEhB,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAC9BlD,SAAS,EAAC,0DAA0D;MAAA+C,QAAA,eAEpEtD,OAAA;QAAKO,SAAS,EAAC,iCAAiC;QAAA+C,QAAA,GAE3Cc,UAAU,iBACPpE,OAAA;UAAKO,SAAS,EAAC,QAAQ;UAAA+C,QAAA,eACnBtD,OAAA;YAAKO,SAAS,EAAC,UAAU;YAAA+C,QAAA,gBACrBtD,OAAA,CAACP,QAAQ;cAACc,SAAS,EAAC;YAA0E;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjG9D,OAAA;cACIsE,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,iBAAiB;cAC7BC,KAAK,EAAElD,UAAW;cAClBmD,QAAQ,EAAGC,CAAC,IAAKC,aAAa,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;cAC/CjE,SAAS,EAAC;YAAoH;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAGAO,WAAW,iBACRrE,OAAA;UAAKO,SAAS,EAAC,YAAY;UAAA+C,QAAA,gBACvBtD,OAAA;YACIwE,KAAK,EAAEhD,YAAa;YACpBiD,QAAQ,EAAGC,CAAC,IAAKG,eAAe,CAACH,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;YACjDjE,SAAS,EAAC,uGAAuG;YAAA+C,QAAA,gBAEjHtD,OAAA;cAAQwE,KAAK,EAAC,KAAK;cAAAlB,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9D,OAAA;cAAQwE,KAAK,EAAC,SAAS;cAAAlB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9D,OAAA;cAAQwE,KAAK,EAAC,MAAM;cAAAlB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC9D,OAAA;cAAQwE,KAAK,EAAC,SAAS;cAAAlB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAET9D,OAAA;YACIwE,KAAK,EAAE5C,MAAO;YACd6C,QAAQ,EAAGC,CAAC,IAAKI,SAAS,CAACJ,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;YAC3CjE,SAAS,EAAC,uGAAuG;YAAA+C,QAAA,gBAEjHtD,OAAA;cAAQwE,KAAK,EAAC,MAAM;cAAAlB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC9D,OAAA;cAAQwE,KAAK,EAAC,QAAQ;cAAAlB,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzC9D,OAAA;cAAQwE,KAAK,EAAC,MAAM;cAAAlB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,eAGD9D,OAAA,CAACT,MAAM,CAACgE,GAAG;MACPwB,QAAQ,EAAEvC,iBAAkB;MAC5BgB,OAAO,EAAC,QAAQ;MAChBE,OAAO,EAAC,SAAS;MACjBnD,SAAS,EAAEgC,gBAAgB,CAAC,CAAE;MAAAe,QAAA,eAE9BtD,OAAA,CAACR,eAAe;QAAA8D,QAAA,EACX1C,aAAa,CAACyC,GAAG,CAAC,CAACvC,IAAI,EAAEkE,KAAK,KAAK;UAChC,MAAMC,aAAa,GAAGnE,IAAI,CAACoE,MAAM,KAAK/E,aAAa,IAAIW,IAAI,CAACqE,GAAG,KAAKhF,aAAa;UACjF,MAAM8B,IAAI,GAAG+C,KAAK,GAAG,CAAC;UAEtB,oBACIhF,OAAA,CAACT,MAAM,CAACgE,GAAG;YAEP6B,GAAG,EAAEH,aAAa,GAAGtE,cAAc,GAAG,IAAK;YAC3CP,MAAM;YACNoD,OAAO,EAAE;cAAEd,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAI,CAAE;YACpCP,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAE,CAAE;YAClCoB,IAAI,EAAE;cAAE3C,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAI,CAAE;YACjCrB,UAAU,EAAE;cAAE0C,QAAQ,EAAE;YAAI,CAAE;YAAAhC,QAAA,eAE9BtD,OAAA,CAACF,eAAe;cACZgB,IAAI,EAAEA,IAAK;cACXmB,IAAI,EAAEA,IAAK;cACXgD,aAAa,EAAEA,aAAc;cAC7B7E,MAAM,EAAEA,MAAO;cACfC,IAAI,EAAEA,IAAK;cACXC,SAAS,EAAEA;YAAU;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC,GAfGhD,IAAI,CAACoE,MAAM,IAAIpE,IAAI,CAACqE,GAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBpB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGZlD,aAAa,CAACmC,MAAM,KAAK,CAAC,iBACvB/C,OAAA,CAACT,MAAM,CAACgE,GAAG;MACPC,OAAO,EAAE;QAAEd,OAAO,EAAE;MAAE,CAAE;MACxBgB,OAAO,EAAE;QAAEhB,OAAO,EAAE;MAAE,CAAE;MACxBnC,SAAS,EAAC,mBAAmB;MAAA+C,QAAA,gBAE7BtD,OAAA,CAACJ,OAAO;QAACW,SAAS,EAAC;MAAsC;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D9D,OAAA;QAAIO,SAAS,EAAC,wCAAwC;QAAA+C,QAAA,EAAC;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E9D,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAA+C,QAAA,EACvBhC,UAAU,IAAIE,YAAY,KAAK,KAAK,GAC/B,sCAAsC,GACtC;MAA+B;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAGA3D,aAAa,IAAIS,aAAa,CAACmC,MAAM,GAAG,EAAE,iBACvC/C,OAAA,CAACT,MAAM,CAACwE,MAAM;MACVP,OAAO,EAAE;QAAEd,OAAO,EAAE,CAAC;QAAEuB,KAAK,EAAE;MAAE,CAAE;MAClCP,OAAO,EAAE;QAAEhB,OAAO,EAAE,CAAC;QAAEuB,KAAK,EAAE;MAAE,CAAE;MAClCD,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MACzBE,OAAO,EAAEjC,mBAAoB;MAC7B3B,SAAS,EAAC,6IAA6I;MACvJgF,KAAK,EAAC,oBAAoB;MAAAjC,QAAA,eAE1BtD,OAAA,CAACL,MAAM;QAACY,SAAS,EAAC;MAAS;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACtD,EAAA,CAjQIP,eAAe;AAAAuF,EAAA,GAAfvF,eAAe;AAmQrB,eAAeA,eAAe;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}