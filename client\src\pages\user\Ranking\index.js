import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { motion } from "framer-motion";
import { TbTrophy, TbMedal, TbCrown, TbRefresh, TbAlertCircle, TbArrowLeft } from "react-icons/tb";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import UserRankingList from "../../../components/modern/UserRankingList";
import { message } from "antd";

const Ranking = () => {
    const userState = useSelector((state) => state.users || {});
    const { user } = userState;
    const [rankingData, setRankingData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [refreshing, setRefreshing] = useState(false);

    const fetchRankingData = async (showRefreshMessage = false) => {
        try {
            if (showRefreshMessage) {
                setRefreshing(true);
                message.loading("Refreshing rankings...", 1);
            }

            // Try enhanced leaderboard first, fallback to regular ranking
            let response;
            try {
                const enhancedResponse = await fetch('/api/quiz/enhanced-leaderboard', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                const enhancedData = await enhancedResponse.json();

                if (enhancedData.success) {
                    response = enhancedData;
                } else {
                    throw new Error('Enhanced leaderboard failed');
                }
            } catch (enhancedError) {
                console.log('Falling back to regular ranking:', enhancedError);
                response = await getAllReportsForRanking();
            }

            if (response.success) {
                // Transform data to match UserRankingCard expectations
                const transformedData = response.data.map((userData, index) => ({
                    userId: userData.userId || userData._id,
                    _id: userData.userId || userData._id,
                    name: userData.userName || userData.name,
                    profilePicture: userData.userPhoto || userData.profileImage,
                    school: userData.userSchool || userData.school,
                    class: userData.userClass || userData.class,
                    level: userData.userLevel || userData.level,
                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,
                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,
                    passedExamsCount: userData.passedExamsCount || 0,
                    retryCount: userData.retryCount || 0,
                    scoreRatio: userData.scoreRatio || 0,
                    averageScore: userData.averageScore || 0,
                    bestStreak: userData.bestStreak || 0,
                    currentStreak: userData.currentStreak || 0,
                    achievements: userData.achievements || [],
                    rankingScore: userData.enhancedRankingScore || userData.rankingScore || userData.totalPoints || 0,
                    rank: userData.rank || index + 1,
                    subscriptionStatus: userData.subscriptionStatus || 'free'
                }));

                setRankingData(transformedData);
                setError(null);

                if (showRefreshMessage) {
                    message.success("Rankings updated successfully!");
                }
            } else {
                setError(response.message || "Failed to fetch ranking data");
                message.error("Failed to load rankings");
            }
        } catch (err) {
            console.error('Ranking fetch error:', err);
            setError(err.message || "An error occurred while fetching rankings");
            message.error("Network error while loading rankings");
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchRankingData();
    }, []);

    const handleRefresh = () => {
        fetchRankingData(true);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
                <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center bg-white rounded-xl p-8 shadow-lg"
                >
                    <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
                    />
                    <p className="text-gray-600 font-medium">Loading rankings...</p>
                </motion.div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full"
                >
                    <TbAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Rankings</h2>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleRefresh}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto"
                    >
                        <TbRefresh className="w-5 h-5" />
                        <span>Try Again</span>
                    </motion.button>
                </motion.div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
            {/* Back Button Only */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4"
            >
                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => window.history.back()}
                    className="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 shadow-sm border"
                >
                    <TbArrowLeft className="w-5 h-5" />
                    <span>Back</span>
                </motion.button>
            </motion.div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
                {rankingData.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="text-center py-16 bg-white rounded-xl shadow-sm"
                    >
                        <TbMedal className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Rankings Available</h3>
                        <p className="text-gray-600 mb-6">Complete some quizzes to see your ranking!</p>
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleRefresh}
                            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                        >
                            Refresh Rankings
                        </motion.button>
                    </motion.div>
                ) : (
                    <UserRankingList
                        users={rankingData}
                        currentUserId={user?._id || null}
                        layout="horizontal"
                        size="medium"
                        showStats={true}
                        className="space-y-6"
                    />
                )}
            </div>
        </div>
    );
};

export default Ranking;