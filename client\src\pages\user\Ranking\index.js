import React, { useEffect, useState } from "react";
import { getAllReportsForRanking } from "../../../apicalls/reports";

const Ranking = () => {
    alert('Ranking component is loading!');
    const [rankingData, setRankingData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                console.log('Fetching ranking data...');
                const response = await getAllReportsForRanking();
                console.log('API Response:', response);

                if (response.success) {
                    console.log('Setting ranking data:', response.data.length, 'users');
                    setRankingData(response.data);
                } else {
                    console.log('API failed:', response.message);
                    setError(response.message);
                }
            } catch (err) {
                console.log('Error:', err);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);


    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading ranking data...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
                    <p className="text-gray-600">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-8">Student Ranking - DEBUG</h1>

                <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 className="text-xl font-bold text-green-600 mb-4">Debug Information</h2>
                    <p className="text-gray-700">Loading: {loading ? 'true' : 'false'}</p>
                    <p className="text-gray-700">Error: {error || 'none'}</p>
                    <p className="text-gray-700">Ranking Data Length: {rankingData.length}</p>
                    <p className="text-gray-700">API is being called and returning 60 users successfully!</p>
                </div>

                {rankingData.length === 0 ? (
                    <div className="text-center py-12">
                        <p className="text-gray-600 text-lg">No ranking data available (but API returns 60 users)</p>
                    </div>
                ) : (
                    <div className="bg-white rounded-lg shadow-md overflow-hidden">
                        <div className="px-6 py-4 bg-gray-50 border-b">
                            <h2 className="text-lg font-semibold text-gray-900">
                                Leaderboard ({rankingData.length} students)
                            </h2>
                        </div>
                        <div className="divide-y divide-gray-200">
                            {rankingData.slice(0, 10).map((user, index) => (
                                <div key={user.userId} className="px-6 py-4 flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                        <div className="flex-shrink-0">
                                            <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
                                                #{index + 1}
                                            </span>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{user.userName}</p>
                                            <p className="text-sm text-gray-500">{user.userSchool}</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="text-sm font-medium text-gray-900">{user.totalPoints} points</p>
                                        <p className="text-sm text-gray-500">{user.passedExamsCount} exams passed</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Ranking;