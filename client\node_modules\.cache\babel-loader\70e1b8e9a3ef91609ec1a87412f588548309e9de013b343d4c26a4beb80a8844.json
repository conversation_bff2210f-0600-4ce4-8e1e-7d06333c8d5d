{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSearch, Tb<PERSON><PERSON>er, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showSearch = true,\n  showFilters = true,\n  showStats = true,\n  className = ''\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'premium', 'free', 'expired'\n  const [sortBy, setSortBy] = useState('rank'); // 'rank', 'points', 'name'\n  const currentUserRef = useRef(null);\n\n  // Filter and sort users\n  const filteredUsers = users.filter(user => {\n    // Add null checks for user properties\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || '';\n    const userSubscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || 'free';\n    const matchesSearch = userName.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterStatus === 'all' || filterStatus === 'premium' && (userSubscriptionStatus === 'active' || userSubscriptionStatus === 'premium') || filterStatus === 'free' && userSubscriptionStatus === 'free' || filterStatus === 'expired' && userSubscriptionStatus === 'expired';\n    return matchesSearch && matchesFilter;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'points':\n        return (b.totalPoints || 0) - (a.totalPoints || 0);\n      case 'name':\n        const nameA = (a === null || a === void 0 ? void 0 : a.name) || '';\n        const nameB = (b === null || b === void 0 ? void 0 : b.name) || '';\n        return nameA.localeCompare(nameB);\n      case 'rank':\n      default:\n        return (a.rank || 0) - (b.rank || 0);\n    }\n  });\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n    }\n  };\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n      case 'horizontal':\n      default:\n        return 'space-y-3';\n    }\n  };\n\n  // Container animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Stats summary\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium').length;\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.totalPoints || 0)) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-6 h-6 text-yellow-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Leaderboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 25\n        }, this), currentUserId && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: scrollToCurrentUser,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbUser, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Find Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-5 h-5 text-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: topScore\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 17\n    }, this), (showSearch || showFilters) && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-white rounded-xl p-4 border border-gray-200 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [showSearch && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search users...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 29\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterStatus,\n            onChange: e => setFilterStatus(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"premium\",\n              children: \"Premium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"free\",\n              children: \"Free\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"expired\",\n              children: \"Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"rank\",\n              children: \"By Rank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"points\",\n              children: \"By Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name\",\n              children: \"By Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      className: getLayoutClasses(),\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: filteredUsers.map((user, index) => {\n          const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n          const rank = index + 1;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            ref: isCurrentUser ? currentUserRef : null,\n            layout: true,\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: user,\n              rank: rank,\n              isCurrentUser: isCurrentUser,\n              layout: layout,\n              size: size,\n              showStats: showStats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this), filteredUsers.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: searchTerm || filterStatus !== 'all' ? 'Try adjusting your search or filters' : 'No users available to display'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 17\n    }, this), currentUserId && filteredUsers.length > 10 && /*#__PURE__*/_jsxDEV(motion.button, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"lhy4RJuLGG+F+z3JA+CCenjU9c8=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "TbSearch", "Tb<PERSON><PERSON>er", "TbUser", "TbUsers", "TbTrophy", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showSearch", "showFilters", "showStats", "className", "_s", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "sortBy", "setSortBy", "currentUserRef", "filteredUsers", "filter", "user", "userName", "name", "userSubscriptionStatus", "subscriptionStatus", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "sort", "a", "b", "totalPoints", "nameA", "nameB", "localeCompare", "rank", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "getLayoutClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "totalUsers", "length", "premiumUsers", "u", "topScore", "Math", "max", "map", "children", "div", "initial", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "variants", "index", "isCurrentUser", "userId", "_id", "ref", "exit", "duration", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSearch, TbFilter, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({ \n    users = [], \n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showSearch = true,\n    showFilters = true,\n    showStats = true,\n    className = ''\n}) => {\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'premium', 'free', 'expired'\n    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'points', 'name'\n    const currentUserRef = useRef(null);\n\n    // Filter and sort users\n    const filteredUsers = users\n        .filter(user => {\n            // Add null checks for user properties\n            const userName = user?.name || '';\n            const userSubscriptionStatus = user?.subscriptionStatus || 'free';\n\n            const matchesSearch = userName.toLowerCase().includes(searchTerm.toLowerCase());\n            const matchesFilter = filterStatus === 'all' ||\n                (filterStatus === 'premium' && (userSubscriptionStatus === 'active' || userSubscriptionStatus === 'premium')) ||\n                (filterStatus === 'free' && userSubscriptionStatus === 'free') ||\n                (filterStatus === 'expired' && userSubscriptionStatus === 'expired');\n            return matchesSearch && matchesFilter;\n        })\n        .sort((a, b) => {\n            switch (sortBy) {\n                case 'points':\n                    return (b.totalPoints || 0) - (a.totalPoints || 0);\n                case 'name':\n                    const nameA = a?.name || '';\n                    const nameB = b?.name || '';\n                    return nameA.localeCompare(nameB);\n                case 'rank':\n                default:\n                    return (a.rank || 0) - (b.rank || 0);\n            }\n        });\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (currentUserRef.current) {\n            currentUserRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n        }\n    };\n\n    // Get layout classes\n    const getLayoutClasses = () => {\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n            case 'horizontal':\n            default:\n                return 'space-y-3';\n        }\n    };\n\n    // Container animation variants\n    const containerVariants = {\n        hidden: { opacity: 0 },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n\n    // Stats summary\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium').length;\n    const topScore = users.length > 0 ? Math.max(...users.map(u => u.totalPoints || 0)) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\"\n                >\n                    <div className=\"flex items-center justify-between mb-4\">\n                        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">\n                            <TbTrophy className=\"w-6 h-6 text-yellow-500\" />\n                            <span>Leaderboard</span>\n                        </h2>\n                        \n                        {currentUserId && (\n                            <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={scrollToCurrentUser}\n                                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\"\n                            >\n                                <TbUser className=\"w-4 h-4\" />\n                                <span>Find Me</span>\n                            </motion.button>\n                        )}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUsers className=\"w-5 h-5 text-blue-500\" />\n                                <span className=\"text-sm text-gray-600\">Total Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{totalUsers}</div>\n                        </div>\n                        \n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbTrophy className=\"w-5 h-5 text-yellow-500\" />\n                                <span className=\"text-sm text-gray-600\">Premium Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{premiumUsers}</div>\n                        </div>\n                        \n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUser className=\"w-5 h-5 text-green-500\" />\n                                <span className=\"text-sm text-gray-600\">Top Score</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{topScore}</div>\n                        </div>\n                    </div>\n                </motion.div>\n            )}\n\n            {/* Search and Filters */}\n            {(showSearch || showFilters) && (\n                <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-white rounded-xl p-4 border border-gray-200 shadow-sm\"\n                >\n                    <div className=\"flex flex-col sm:flex-row gap-4\">\n                        {/* Search */}\n                        {showSearch && (\n                            <div className=\"flex-1\">\n                                <div className=\"relative\">\n                                    <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                                    <input\n                                        type=\"text\"\n                                        placeholder=\"Search users...\"\n                                        value={searchTerm}\n                                        onChange={(e) => setSearchTerm(e.target.value)}\n                                        className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    />\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Filters */}\n                        {showFilters && (\n                            <div className=\"flex gap-2\">\n                                <select\n                                    value={filterStatus}\n                                    onChange={(e) => setFilterStatus(e.target.value)}\n                                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                >\n                                    <option value=\"all\">All Users</option>\n                                    <option value=\"premium\">Premium</option>\n                                    <option value=\"free\">Free</option>\n                                    <option value=\"expired\">Expired</option>\n                                </select>\n\n                                <select\n                                    value={sortBy}\n                                    onChange={(e) => setSortBy(e.target.value)}\n                                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                >\n                                    <option value=\"rank\">By Rank</option>\n                                    <option value=\"points\">By Points</option>\n                                    <option value=\"name\">By Name</option>\n                                </select>\n                            </div>\n                        )}\n                    </div>\n                </motion.div>\n            )}\n\n            {/* User List */}\n            <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className={getLayoutClasses()}\n            >\n                <AnimatePresence>\n                    {filteredUsers.map((user, index) => {\n                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                        const rank = index + 1;\n                        \n                        return (\n                            <motion.div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? currentUserRef : null}\n                                layout\n                                initial={{ opacity: 0, scale: 0.9 }}\n                                animate={{ opacity: 1, scale: 1 }}\n                                exit={{ opacity: 0, scale: 0.9 }}\n                                transition={{ duration: 0.2 }}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </motion.div>\n                        );\n                    })}\n                </AnimatePresence>\n            </motion.div>\n\n            {/* Empty State */}\n            {filteredUsers.length === 0 && (\n                <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    className=\"text-center py-12\"\n                >\n                    <TbUsers className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        {searchTerm || filterStatus !== 'all' \n                            ? 'Try adjusting your search or filters'\n                            : 'No users available to display'\n                        }\n                    </p>\n                </motion.div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && filteredUsers.length > 10 && (\n                <motion.button\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </motion.button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9E,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,UAAU,GAAG,IAAI;EACjBC,WAAW,GAAG,IAAI;EAClBC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM4B,cAAc,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM4B,aAAa,GAAGhB,KAAK,CACtBiB,MAAM,CAACC,IAAI,IAAI;IACZ;IACA,MAAMC,QAAQ,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,KAAI,EAAE;IACjC,MAAMC,sBAAsB,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,kBAAkB,KAAI,MAAM;IAEjE,MAAMC,aAAa,GAAGJ,QAAQ,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,CAAC,CAAC,CAAC;IAC/E,MAAME,aAAa,GAAGf,YAAY,KAAK,KAAK,IACvCA,YAAY,KAAK,SAAS,KAAKU,sBAAsB,KAAK,QAAQ,IAAIA,sBAAsB,KAAK,SAAS,CAAE,IAC5GV,YAAY,KAAK,MAAM,IAAIU,sBAAsB,KAAK,MAAO,IAC7DV,YAAY,KAAK,SAAS,IAAIU,sBAAsB,KAAK,SAAU;IACxE,OAAOE,aAAa,IAAIG,aAAa;EACzC,CAAC,CAAC,CACDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACZ,QAAQhB,MAAM;MACV,KAAK,QAAQ;QACT,OAAO,CAACgB,CAAC,CAACC,WAAW,IAAI,CAAC,KAAKF,CAAC,CAACE,WAAW,IAAI,CAAC,CAAC;MACtD,KAAK,MAAM;QACP,MAAMC,KAAK,GAAG,CAAAH,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAER,IAAI,KAAI,EAAE;QAC3B,MAAMY,KAAK,GAAG,CAAAH,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAET,IAAI,KAAI,EAAE;QAC3B,OAAOW,KAAK,CAACE,aAAa,CAACD,KAAK,CAAC;MACrC,KAAK,MAAM;MACX;QACI,OAAO,CAACJ,CAAC,CAACM,IAAI,IAAI,CAAC,KAAKL,CAAC,CAACK,IAAI,IAAI,CAAC,CAAC;IAC5C;EACJ,CAAC,CAAC;;EAEN;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIpB,cAAc,CAACqB,OAAO,EAAE;MACxBrB,cAAc,CAACqB,OAAO,CAACC,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQtC,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAED;EACA,MAAMuC,iBAAiB,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACRC,eAAe,EAAE;MACrB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG/C,KAAK,CAACgD,MAAM;EAC/B,MAAMC,YAAY,GAAGjD,KAAK,CAACiB,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAAC5B,kBAAkB,KAAK,QAAQ,IAAI4B,CAAC,CAAC5B,kBAAkB,KAAK,SAAS,CAAC,CAAC0B,MAAM;EACtH,MAAMG,QAAQ,GAAGnD,KAAK,CAACgD,MAAM,GAAG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAGrD,KAAK,CAACsD,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACpB,WAAW,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EAEvF,oBACIhC,OAAA;IAAKS,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAAgD,QAAA,GAEpCjD,SAAS,iBACNR,OAAA,CAACT,MAAM,CAACmE,GAAG;MACPC,OAAO,EAAE;QAAEd,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEhB,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAC9BnD,SAAS,EAAC,kFAAkF;MAAAgD,QAAA,gBAE5FzD,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAgD,QAAA,gBACnDzD,OAAA;UAAIS,SAAS,EAAC,8DAA8D;UAAAgD,QAAA,gBACxEzD,OAAA,CAACH,QAAQ;YAACY,SAAS,EAAC;UAAyB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDjE,OAAA;YAAAyD,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EAEJ9D,aAAa,iBACVH,OAAA,CAACT,MAAM,CAAC2E,MAAM;UACVC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BE,OAAO,EAAEjC,mBAAoB;UAC7B5B,SAAS,EAAC,sIAAsI;UAAAgD,QAAA,gBAEhJzD,OAAA,CAACL,MAAM;YAACc,SAAS,EAAC;UAAS;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BjE,OAAA;YAAAyD,QAAA,EAAM;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAENjE,OAAA;QAAKS,SAAS,EAAC,uCAAuC;QAAAgD,QAAA,gBAClDzD,OAAA;UAAKS,SAAS,EAAC,gDAAgD;UAAAgD,QAAA,gBAC3DzD,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAgD,QAAA,gBACxCzD,OAAA,CAACJ,OAAO;cAACa,SAAS,EAAC;YAAuB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CjE,OAAA;cAAMS,SAAS,EAAC,uBAAuB;cAAAgD,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNjE,OAAA;YAAKS,SAAS,EAAC,uCAAuC;YAAAgD,QAAA,EAAER;UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eAENjE,OAAA;UAAKS,SAAS,EAAC,gDAAgD;UAAAgD,QAAA,gBAC3DzD,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAgD,QAAA,gBACxCzD,OAAA,CAACH,QAAQ;cAACY,SAAS,EAAC;YAAyB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDjE,OAAA;cAAMS,SAAS,EAAC,uBAAuB;cAAAgD,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNjE,OAAA;YAAKS,SAAS,EAAC,uCAAuC;YAAAgD,QAAA,EAAEN;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAENjE,OAAA;UAAKS,SAAS,EAAC,gDAAgD;UAAAgD,QAAA,gBAC3DzD,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAgD,QAAA,gBACxCzD,OAAA,CAACL,MAAM;cAACc,SAAS,EAAC;YAAwB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CjE,OAAA;cAAMS,SAAS,EAAC,uBAAuB;cAAAgD,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNjE,OAAA;YAAKS,SAAS,EAAC,uCAAuC;YAAAgD,QAAA,EAAEJ;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,EAGA,CAAC3D,UAAU,IAAIC,WAAW,kBACvBP,OAAA,CAACT,MAAM,CAACmE,GAAG;MACPC,OAAO,EAAE;QAAEd,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEhB,OAAO,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAC9BnD,SAAS,EAAC,0DAA0D;MAAAgD,QAAA,eAEpEzD,OAAA;QAAKS,SAAS,EAAC,iCAAiC;QAAAgD,QAAA,GAE3CnD,UAAU,iBACPN,OAAA;UAAKS,SAAS,EAAC,QAAQ;UAAAgD,QAAA,eACnBzD,OAAA;YAAKS,SAAS,EAAC,UAAU;YAAAgD,QAAA,gBACrBzD,OAAA,CAACP,QAAQ;cAACgB,SAAS,EAAC;YAA0E;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGjE,OAAA;cACIuE,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,iBAAiB;cAC7BC,KAAK,EAAE9D,UAAW;cAClB+D,QAAQ,EAAGC,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/ChE,SAAS,EAAC;YAAoH;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAGA1D,WAAW,iBACRP,OAAA;UAAKS,SAAS,EAAC,YAAY;UAAAgD,QAAA,gBACvBzD,OAAA;YACIyE,KAAK,EAAE5D,YAAa;YACpB6D,QAAQ,EAAGC,CAAC,IAAK7D,eAAe,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjDhE,SAAS,EAAC,uGAAuG;YAAAgD,QAAA,gBAEjHzD,OAAA;cAAQyE,KAAK,EAAC,KAAK;cAAAhB,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCjE,OAAA;cAAQyE,KAAK,EAAC,SAAS;cAAAhB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCjE,OAAA;cAAQyE,KAAK,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCjE,OAAA;cAAQyE,KAAK,EAAC,SAAS;cAAAhB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAETjE,OAAA;YACIyE,KAAK,EAAE1D,MAAO;YACd2D,QAAQ,EAAGC,CAAC,IAAK3D,SAAS,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3ChE,SAAS,EAAC,uGAAuG;YAAAgD,QAAA,gBAEjHzD,OAAA;cAAQyE,KAAK,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCjE,OAAA;cAAQyE,KAAK,EAAC,QAAQ;cAAAhB,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzCjE,OAAA;cAAQyE,KAAK,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,eAGDjE,OAAA,CAACT,MAAM,CAACmE,GAAG;MACPmB,QAAQ,EAAElC,iBAAkB;MAC5BgB,OAAO,EAAC,QAAQ;MAChBE,OAAO,EAAC,SAAS;MACjBpD,SAAS,EAAEiC,gBAAgB,CAAC,CAAE;MAAAe,QAAA,eAE9BzD,OAAA,CAACR,eAAe;QAAAiE,QAAA,EACXvC,aAAa,CAACsC,GAAG,CAAC,CAACpC,IAAI,EAAE0D,KAAK,KAAK;UAChC,MAAMC,aAAa,GAAG3D,IAAI,CAAC4D,MAAM,KAAK7E,aAAa,IAAIiB,IAAI,CAAC6D,GAAG,KAAK9E,aAAa;UACjF,MAAMiC,IAAI,GAAG0C,KAAK,GAAG,CAAC;UAEtB,oBACI9E,OAAA,CAACT,MAAM,CAACmE,GAAG;YAEPwB,GAAG,EAAEH,aAAa,GAAG9D,cAAc,GAAG,IAAK;YAC3Cb,MAAM;YACNuD,OAAO,EAAE;cAAEd,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAI,CAAE;YACpCP,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAE,CAAE;YAClCe,IAAI,EAAE;cAAEtC,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAI,CAAE;YACjCrB,UAAU,EAAE;cAAEqC,QAAQ,EAAE;YAAI,CAAE;YAAA3B,QAAA,eAE9BzD,OAAA,CAACF,eAAe;cACZsB,IAAI,EAAEA,IAAK;cACXgB,IAAI,EAAEA,IAAK;cACX2C,aAAa,EAAEA,aAAc;cAC7B3E,MAAM,EAAEA,MAAO;cACfC,IAAI,EAAEA,IAAK;cACXG,SAAS,EAAEA;YAAU;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC,GAfG7C,IAAI,CAAC4D,MAAM,IAAI5D,IAAI,CAAC6D,GAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBpB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGZ/C,aAAa,CAACgC,MAAM,KAAK,CAAC,iBACvBlD,OAAA,CAACT,MAAM,CAACmE,GAAG;MACPC,OAAO,EAAE;QAAEd,OAAO,EAAE;MAAE,CAAE;MACxBgB,OAAO,EAAE;QAAEhB,OAAO,EAAE;MAAE,CAAE;MACxBpC,SAAS,EAAC,mBAAmB;MAAAgD,QAAA,gBAE7BzD,OAAA,CAACJ,OAAO;QAACa,SAAS,EAAC;MAAsC;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5DjE,OAAA;QAAIS,SAAS,EAAC,wCAAwC;QAAAgD,QAAA,EAAC;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EjE,OAAA;QAAGS,SAAS,EAAC,eAAe;QAAAgD,QAAA,EACvB9C,UAAU,IAAIE,YAAY,KAAK,KAAK,GAC/B,sCAAsC,GACtC;MAA+B;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAGA9D,aAAa,IAAIe,aAAa,CAACgC,MAAM,GAAG,EAAE,iBACvClD,OAAA,CAACT,MAAM,CAAC2E,MAAM;MACVP,OAAO,EAAE;QAAEd,OAAO,EAAE,CAAC;QAAEuB,KAAK,EAAE;MAAE,CAAE;MAClCP,OAAO,EAAE;QAAEhB,OAAO,EAAE,CAAC;QAAEuB,KAAK,EAAE;MAAE,CAAE;MAClCD,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MACzBE,OAAO,EAAEjC,mBAAoB;MAC7B5B,SAAS,EAAC,6IAA6I;MACvJ4E,KAAK,EAAC,oBAAoB;MAAA5B,QAAA,eAE1BzD,OAAA,CAACL,MAAM;QAACc,SAAS,EAAC;MAAS;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACvD,EAAA,CArQIT,eAAe;AAAAqF,EAAA,GAAfrF,eAAe;AAuQrB,eAAeA,eAAe;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}