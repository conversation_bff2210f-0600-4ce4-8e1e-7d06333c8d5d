{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingCard.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbTrophy, TbMedal, TbCrown, TbStar, TbUsers, TbFlame, TbTarget } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport './UserRankingCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingCard = ({\n  user,\n  rank,\n  isCurrentUser = false,\n  layout = 'horizontal',\n  // 'horizontal' or 'vertical'\n  size = 'medium',\n  // 'small', 'medium', 'large'\n  showStats = true,\n  className = ''\n}) => {\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      avatar: 'w-10 h-10',\n      text: 'text-sm',\n      subtext: 'text-xs',\n      padding: 'p-3',\n      spacing: 'space-x-3'\n    },\n    medium: {\n      avatar: 'w-12 h-12',\n      text: 'text-base',\n      subtext: 'text-sm',\n      padding: 'p-4',\n      spacing: 'space-x-4'\n    },\n    large: {\n      avatar: 'w-16 h-16',\n      text: 'text-lg',\n      subtext: 'text-base',\n      padding: 'p-5',\n      spacing: 'space-x-5'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Get subscription status styling\n  const getSubscriptionStyling = () => {\n    const subscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || 'free';\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      return {\n        avatarClass: 'avatar-premium premium-glow',\n        badge: 'status-premium',\n        glow: 'shadow-lg shadow-yellow-400/50'\n      };\n    } else if (subscriptionStatus === 'free') {\n      return {\n        avatarClass: 'avatar-free',\n        badge: 'status-free',\n        glow: 'shadow-md shadow-blue-400/30'\n      };\n    } else {\n      return {\n        avatarClass: 'avatar-expired',\n        badge: 'status-expired',\n        glow: 'shadow-sm'\n      };\n    }\n  };\n  const styling = getSubscriptionStyling();\n\n  // Get rank icon and color\n  const getRankDisplay = () => {\n    if (rank === 1) {\n      return {\n        icon: TbCrown,\n        color: 'text-yellow-500',\n        bg: 'bg-yellow-50'\n      };\n    } else if (rank === 2) {\n      return {\n        icon: TbMedal,\n        color: 'text-gray-400',\n        bg: 'bg-gray-50'\n      };\n    } else if (rank === 3) {\n      return {\n        icon: TbTrophy,\n        color: 'text-amber-600',\n        bg: 'bg-amber-50'\n      };\n    } else if (rank <= 10) {\n      return {\n        icon: TbStar,\n        color: 'text-blue-500',\n        bg: 'bg-blue-50'\n      };\n    } else {\n      return {\n        icon: null,\n        color: 'text-gray-500',\n        bg: 'bg-gray-50'\n      };\n    }\n  };\n  const rankDisplay = getRankDisplay();\n  const RankIcon = rankDisplay.icon;\n\n  // Animation variants\n  const cardVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.3\n      }\n    },\n    hover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n  const avatarVariants = {\n    hover: {\n      scale: 1.1,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n\n  // Avatar wrapper with subscription styling\n  const StyledAvatar = ({\n    children\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${config.avatar} ${styling.avatarClass} ${styling.glow}`,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this);\n  };\n  if (layout === 'vertical') {\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: cardVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      whileHover: \"hover\",\n      className: `\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    bg-white rounded-xl border border-gray-200\n                    ${isCurrentUser ? 'current-user-card' : ''}\n                    ${className}\n                `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    rank-badge flex items-center justify-center w-8 h-8 rounded-full mb-3\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `,\n        children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-bold\",\n          children: [\"#\", rank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: avatarVariants,\n        whileHover: \"hover\",\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(StyledAvatar, {\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.profilePicture || '/default-avatar.png',\n            alt: user.name,\n            className: \"w-full h-full rounded-full object-cover bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate max-w-24`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${config.subtext} text-gray-500`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this), user.averageScore && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${config.subtext} text-gray-500`,\n          children: [\"Avg: \", user.averageScore, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 25\n        }, this), user.currentStreak > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 25\n        }, this), user.achievements && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(AchievementList, {\n          achievements: user.achievements,\n          maxDisplay: 3,\n          size: \"small\",\n          layout: \"horizontal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        inline-block px-2 py-1 rounded-full text-xs font-medium\n                        ${styling.badge}\n                    `,\n          children: user.subscriptionStatus === 'active' ? 'Premium' : user.subscriptionStatus === 'free' ? 'Free' : 'Expired'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Horizontal layout (default)\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    variants: cardVariants,\n    initial: \"hidden\",\n    animate: \"visible\",\n    whileHover: \"hover\",\n    className: `\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n                rank-badge flex items-center justify-center w-10 h-10 rounded-full flex-shrink-0\n                ${rankDisplay.bg} ${rankDisplay.color}\n            `,\n      children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-bold\",\n        children: [\"#\", rank]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: avatarVariants,\n      whileHover: \"hover\",\n      className: \"flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(StyledAvatar, {\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.profilePicture || '/default-avatar.png',\n          alt: user.name,\n          className: \"w-full h-full rounded-full object-cover bg-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.badge}\n                    `,\n          children: (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'active' || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'premium' ? 'Premium' : (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' ? 'Free' : 'Expired'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 17\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-gray-600 font-medium`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" points\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.passedExamsCount) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-green-600`,\n          children: [user.passedExamsCount, \" passed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.quizzesTaken) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-blue-600`,\n          children: [user.quizzesTaken, \" quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-gray-600`,\n          children: [user.averageScore, \"% avg\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(AchievementList, {\n          achievements: user.achievements,\n          maxDisplay: 5,\n          size: \"small\",\n          layout: \"horizontal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`,\n        children: user.score || user.totalPoints || 0\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${config.subtext} text-gray-500`,\n        children: \"score\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 9\n  }, this);\n};\n_c = UserRankingCard;\nexport default UserRankingCard;\nvar _c;\n$RefreshReg$(_c, \"UserRankingCard\");", "map": {"version": 3, "names": ["React", "motion", "TbTrophy", "TbMedal", "TbCrown", "TbStar", "TbUsers", "TbFlame", "TbTarget", "AchievementList", "jsxDEV", "_jsxDEV", "UserRankingCard", "user", "rank", "isCurrentUser", "layout", "size", "showStats", "className", "sizeConfig", "small", "avatar", "text", "subtext", "padding", "spacing", "medium", "large", "config", "getSubscriptionStyling", "subscriptionStatus", "avatarClass", "badge", "glow", "styling", "getRankDisplay", "icon", "color", "bg", "rankDisplay", "RankIcon", "cardVariants", "hidden", "opacity", "y", "visible", "transition", "duration", "hover", "scale", "avatar<PERSON><PERSON><PERSON>", "Styled<PERSON>vatar", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "variants", "initial", "animate", "whileHover", "src", "profilePicture", "alt", "name", "totalPoints", "averageScore", "currentStreak", "achievements", "length", "maxDisplay", "passedExamsCount", "undefined", "quizzesTaken", "score", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingCard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbTrophy, TbMedal, TbCrown, TbStar, TbUsers, TbFlame, TbTarget } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport './UserRankingCard.css';\n\nconst UserRankingCard = ({ \n    user, \n    rank, \n    isCurrentUser = false, \n    layout = 'horizontal', // 'horizontal' or 'vertical'\n    size = 'medium', // 'small', 'medium', 'large'\n    showStats = true,\n    className = ''\n}) => {\n    // Size configurations\n    const sizeConfig = {\n        small: {\n            avatar: 'w-10 h-10',\n            text: 'text-sm',\n            subtext: 'text-xs',\n            padding: 'p-3',\n            spacing: 'space-x-3'\n        },\n        medium: {\n            avatar: 'w-12 h-12',\n            text: 'text-base',\n            subtext: 'text-sm',\n            padding: 'p-4',\n            spacing: 'space-x-4'\n        },\n        large: {\n            avatar: 'w-16 h-16',\n            text: 'text-lg',\n            subtext: 'text-base',\n            padding: 'p-5',\n            spacing: 'space-x-5'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Get subscription status styling\n    const getSubscriptionStyling = () => {\n        const subscriptionStatus = user?.subscriptionStatus || 'free';\n\n        if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n            return {\n                avatarClass: 'avatar-premium premium-glow',\n                badge: 'status-premium',\n                glow: 'shadow-lg shadow-yellow-400/50'\n            };\n        } else if (subscriptionStatus === 'free') {\n            return {\n                avatarClass: 'avatar-free',\n                badge: 'status-free',\n                glow: 'shadow-md shadow-blue-400/30'\n            };\n        } else {\n            return {\n                avatarClass: 'avatar-expired',\n                badge: 'status-expired',\n                glow: 'shadow-sm'\n            };\n        }\n    };\n\n    const styling = getSubscriptionStyling();\n\n    // Get rank icon and color\n    const getRankDisplay = () => {\n        if (rank === 1) {\n            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };\n        } else if (rank === 2) {\n            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };\n        } else if (rank === 3) {\n            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };\n        } else if (rank <= 10) {\n            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };\n        } else {\n            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };\n        }\n    };\n\n    const rankDisplay = getRankDisplay();\n    const RankIcon = rankDisplay.icon;\n\n    // Animation variants\n    const cardVariants = {\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n            opacity: 1, \n            y: 0,\n            transition: { duration: 0.3 }\n        },\n        hover: { \n            scale: 1.02,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    const avatarVariants = {\n        hover: { \n            scale: 1.1,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    // Avatar wrapper with subscription styling\n    const StyledAvatar = ({ children }) => {\n        return (\n            <div className={`${config.avatar} ${styling.avatarClass} ${styling.glow}`}>\n                {children}\n            </div>\n        );\n    };\n\n    if (layout === 'vertical') {\n        return (\n            <motion.div\n                variants={cardVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                whileHover=\"hover\"\n                className={`\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    bg-white rounded-xl border border-gray-200\n                    ${isCurrentUser ? 'current-user-card' : ''}\n                    ${className}\n                `}\n            >\n                {/* Rank Badge */}\n                <div className={`\n                    rank-badge flex items-center justify-center w-8 h-8 rounded-full mb-3\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `}>\n                    {RankIcon ? (\n                        <RankIcon className=\"w-4 h-4\" />\n                    ) : (\n                        <span className=\"text-xs font-bold\">#{rank}</span>\n                    )}\n                </div>\n\n                {/* Avatar */}\n                <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"mb-3\">\n                    <StyledAvatar>\n                        <img\n                            src={user.profilePicture || '/default-avatar.png'}\n                            alt={user.name}\n                            className=\"w-full h-full rounded-full object-cover bg-white\"\n                        />\n                    </StyledAvatar>\n                </motion.div>\n\n                {/* User Info */}\n                <div className=\"space-y-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <p className={`${config.subtext} text-gray-500`}>\n                        {user?.totalPoints || 0} pts\n                    </p>\n\n                    {/* Enhanced Stats */}\n                    {user.averageScore && (\n                        <p className={`${config.subtext} text-gray-500`}>\n                            Avg: {user.averageScore}%\n                        </p>\n                    )}\n\n                    {user.currentStreak > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                            <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                {user.currentStreak}\n                            </span>\n                        </div>\n                    )}\n\n                    {/* Achievements */}\n                    {user.achievements && user.achievements.length > 0 && (\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={3}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    )}\n\n                    {/* Subscription Badge */}\n                    <span className={`\n                        inline-block px-2 py-1 rounded-full text-xs font-medium\n                        ${styling.badge}\n                    `}>\n                        {user.subscriptionStatus === 'active' ? 'Premium' :\n                         user.subscriptionStatus === 'free' ? 'Free' : 'Expired'}\n                    </span>\n                </div>\n            </motion.div>\n        );\n    }\n\n    // Horizontal layout (default)\n    return (\n        <motion.div\n            variants={cardVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            whileHover=\"hover\"\n            className={`\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `}\n        >\n            {/* Rank */}\n            <div className={`\n                rank-badge flex items-center justify-center w-10 h-10 rounded-full flex-shrink-0\n                ${rankDisplay.bg} ${rankDisplay.color}\n            `}>\n                {RankIcon ? (\n                    <RankIcon className=\"w-5 h-5\" />\n                ) : (\n                    <span className=\"text-sm font-bold\">#{rank}</span>\n                )}\n            </div>\n\n            {/* Avatar */}\n            <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"flex-shrink-0\">\n                <StyledAvatar>\n                    <img\n                        src={user.profilePicture || '/default-avatar.png'}\n                        alt={user.name}\n                        className=\"w-full h-full rounded-full object-cover bg-white\"\n                    />\n                </StyledAvatar>\n            </motion.div>\n\n            {/* User Info */}\n            <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <span className={`\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.badge}\n                    `}>\n                        {(user?.subscriptionStatus === 'active' || user?.subscriptionStatus === 'premium') ? 'Premium' :\n                         user?.subscriptionStatus === 'free' ? 'Free' : 'Expired'}\n                    </span>\n                </div>\n                \n                {showStats && (\n                    <div className=\"flex items-center space-x-4\">\n                        <span className={`${config.subtext} text-gray-600 font-medium`}>\n                            {user?.totalPoints || 0} points\n                        </span>\n                        {user?.passedExamsCount !== undefined && (\n                            <span className={`${config.subtext} text-green-600`}>\n                                {user.passedExamsCount} passed\n                            </span>\n                        )}\n                        {user?.quizzesTaken !== undefined && (\n                            <span className={`${config.subtext} text-blue-600`}>\n                                {user.quizzesTaken} quizzes\n                            </span>\n                        )}\n                        {user?.averageScore && (\n                            <span className={`${config.subtext} text-gray-600`}>\n                                {user.averageScore}% avg\n                            </span>\n                        )}\n                        {user?.currentStreak > 0 && (\n                            <div className=\"flex items-center space-x-1\">\n                                <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                                <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                    {user.currentStreak}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {/* Achievements for horizontal layout */}\n                {user?.achievements && user.achievements.length > 0 && (\n                    <div className=\"mt-2\">\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={5}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    </div>\n                )}\n            </div>\n\n            {/* Score */}\n            <div className=\"text-right flex-shrink-0\">\n                <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>\n                    {user.score || user.totalPoints || 0}\n                </div>\n                <div className={`${config.subtext} text-gray-500`}>score</div>\n            </div>\n        </motion.div>\n    );\n};\n\nexport default UserRankingCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/F,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EACrBC,IAAI;EACJC,IAAI;EACJC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EAAE;EACjBC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF;EACA,MAAMC,UAAU,GAAG;IACfC,KAAK,EAAE;MACHC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACJL,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDE,KAAK,EAAE;MACHN,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb;EACJ,CAAC;EAED,MAAMG,MAAM,GAAGT,UAAU,CAACH,IAAI,CAAC;;EAE/B;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACjC,MAAMC,kBAAkB,GAAG,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,kBAAkB,KAAI,MAAM;IAE7D,IAAIA,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACrE,OAAO;QACHC,WAAW,EAAE,6BAA6B;QAC1CC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAIH,kBAAkB,KAAK,MAAM,EAAE;MACtC,OAAO;QACHC,WAAW,EAAE,aAAa;QAC1BC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM;MACH,OAAO;QACHF,WAAW,EAAE,gBAAgB;QAC7BC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;MACV,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGL,sBAAsB,CAAC,CAAC;;EAExC;EACA,MAAMM,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAItB,IAAI,KAAK,CAAC,EAAE;MACZ,OAAO;QAAEuB,IAAI,EAAEjC,OAAO;QAAEkC,KAAK,EAAE,iBAAiB;QAAEC,EAAE,EAAE;MAAe,CAAC;IAC1E,CAAC,MAAM,IAAIzB,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAEuB,IAAI,EAAElC,OAAO;QAAEmC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACtE,CAAC,MAAM,IAAIzB,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAEuB,IAAI,EAAEnC,QAAQ;QAAEoC,KAAK,EAAE,gBAAgB;QAAEC,EAAE,EAAE;MAAc,CAAC;IACzE,CAAC,MAAM,IAAIzB,IAAI,IAAI,EAAE,EAAE;MACnB,OAAO;QAAEuB,IAAI,EAAEhC,MAAM;QAAEiC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACrE,CAAC,MAAM;MACH,OAAO;QAAEF,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACnE;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;EACpC,MAAMK,QAAQ,GAAGD,WAAW,CAACH,IAAI;;EAEjC;EACA,MAAMK,YAAY,GAAG;IACjBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,OAAO,EAAE;MACLF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC,CAAC;IACDC,KAAK,EAAE;MACHC,KAAK,EAAE,IAAI;MACXH,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;EAED,MAAMG,cAAc,GAAG;IACnBF,KAAK,EAAE;MACHC,KAAK,EAAE,GAAG;MACVH,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAAC;IAAEC;EAAS,CAAC,KAAK;IACnC,oBACI1C,OAAA;MAAKQ,SAAS,EAAG,GAAEU,MAAM,CAACP,MAAO,IAAGa,OAAO,CAACH,WAAY,IAAGG,OAAO,CAACD,IAAK,EAAE;MAAAmB,QAAA,EACrEA;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd,CAAC;EAED,IAAIzC,MAAM,KAAK,UAAU,EAAE;IACvB,oBACIL,OAAA,CAACV,MAAM,CAACyD,GAAG;MACPC,QAAQ,EAAEjB,YAAa;MACvBkB,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MACjBC,UAAU,EAAC,OAAO;MAClB3C,SAAS,EAAG;AAC5B,0EAA0EU,MAAM,CAACJ,OAAQ;AACzF;AACA,sBAAsBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC/D,sBAAsBI,SAAU;AAChC,iBAAkB;MAAAkC,QAAA,gBAGF1C,OAAA;QAAKQ,SAAS,EAAG;AACjC;AACA,sBAAsBqB,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC1D,iBAAkB;QAAAe,QAAA,EACGZ,QAAQ,gBACL9B,OAAA,CAAC8B,QAAQ;UAACtB,SAAS,EAAC;QAAS;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhC9C,OAAA;UAAMQ,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,GAAC,GAAC,EAACvC,IAAI;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACpD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN9C,OAAA,CAACV,MAAM,CAACyD,GAAG;QAACC,QAAQ,EAAER,cAAe;QAACW,UAAU,EAAC,OAAO;QAAC3C,SAAS,EAAC,MAAM;QAAAkC,QAAA,eACrE1C,OAAA,CAACyC,YAAY;UAAAC,QAAA,eACT1C,OAAA;YACIoD,GAAG,EAAElD,IAAI,CAACmD,cAAc,IAAI,qBAAsB;YAClDC,GAAG,EAAEpD,IAAI,CAACqD,IAAK;YACf/C,SAAS,EAAC;UAAkD;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGb9C,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAkC,QAAA,gBACtB1C,OAAA;UAAIQ,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,kCAAkC;UAAA8B,QAAA,EACzE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,IAAI,KAAI;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACL9C,OAAA;UAAGQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAC3C,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,WAAW,KAAI,CAAC,EAAC,MAC5B;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGH5C,IAAI,CAACuD,YAAY,iBACdzD,OAAA;UAAGQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAAC,OACxC,EAACxC,IAAI,CAACuD,YAAY,EAAC,GAC5B;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACN,EAEA5C,IAAI,CAACwD,aAAa,GAAG,CAAC,iBACnB1D,OAAA;UAAKQ,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,gBACxC1C,OAAA,CAACJ,OAAO;YAACY,SAAS,EAAC;UAAyB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C9C,OAAA;YAAMQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAA6B,QAAA,EAC5DxC,IAAI,CAACwD;UAAa;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR,EAGA5C,IAAI,CAACyD,YAAY,IAAIzD,IAAI,CAACyD,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC9C5D,OAAA,CAACF,eAAe;UACZ6D,YAAY,EAAEzD,IAAI,CAACyD,YAAa;UAChCE,UAAU,EAAE,CAAE;UACdvD,IAAI,EAAC,OAAO;UACZD,MAAM,EAAC;QAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACJ,eAGD9C,OAAA;UAAMQ,SAAS,EAAG;AACtC;AACA,0BAA0BgB,OAAO,CAACF,KAAM;AACxC,qBAAsB;UAAAoB,QAAA,EACGxC,IAAI,CAACkB,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAChDlB,IAAI,CAACkB,kBAAkB,KAAK,MAAM,GAAG,MAAM,GAAG;QAAS;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAErB;;EAEA;EACA,oBACI9C,OAAA,CAACV,MAAM,CAACyD,GAAG;IACPC,QAAQ,EAAEjB,YAAa;IACvBkB,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAC,SAAS;IACjBC,UAAU,EAAC,OAAO;IAClB3C,SAAS,EAAG;AACxB,iDAAiDU,MAAM,CAACH,OAAQ,IAAGG,MAAM,CAACJ,OAAQ;AAClF;AACA,kBAAkBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC3D,kBAAkBI,SAAU;AAC5B,aAAc;IAAAkC,QAAA,gBAGF1C,OAAA;MAAKQ,SAAS,EAAG;AAC7B;AACA,kBAAkBqB,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AACtD,aAAc;MAAAe,QAAA,EACGZ,QAAQ,gBACL9B,OAAA,CAAC8B,QAAQ;QAACtB,SAAS,EAAC;MAAS;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEhC9C,OAAA;QAAMQ,SAAS,EAAC,mBAAmB;QAAAkC,QAAA,GAAC,GAAC,EAACvC,IAAI;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IACpD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN9C,OAAA,CAACV,MAAM,CAACyD,GAAG;MAACC,QAAQ,EAAER,cAAe;MAACW,UAAU,EAAC,OAAO;MAAC3C,SAAS,EAAC,eAAe;MAAAkC,QAAA,eAC9E1C,OAAA,CAACyC,YAAY;QAAAC,QAAA,eACT1C,OAAA;UACIoD,GAAG,EAAElD,IAAI,CAACmD,cAAc,IAAI,qBAAsB;UAClDC,GAAG,EAAEpD,IAAI,CAACqD,IAAK;UACf/C,SAAS,EAAC;QAAkD;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGb9C,OAAA;MAAKQ,SAAS,EAAC,gBAAgB;MAAAkC,QAAA,gBAC3B1C,OAAA;QAAKQ,SAAS,EAAC,kCAAkC;QAAAkC,QAAA,gBAC7C1C,OAAA;UAAIQ,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,yBAAyB;UAAA8B,QAAA,EAChE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,IAAI,KAAI;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACL9C,OAAA;UAAMQ,SAAS,EAAG;AACtC;AACA,0BAA0BgB,OAAO,CAACF,KAAM;AACxC,qBAAsB;UAAAoB,QAAA,EACI,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,kBAAkB,MAAK,QAAQ,IAAI,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,kBAAkB,MAAK,SAAS,GAAI,SAAS,GAC7F,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,kBAAkB,MAAK,MAAM,GAAG,MAAM,GAAG;QAAS;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvC,SAAS,iBACNP,OAAA;QAAKQ,SAAS,EAAC,6BAA6B;QAAAkC,QAAA,gBACxC1C,OAAA;UAAMQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;UAAA6B,QAAA,GAC1D,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,WAAW,KAAI,CAAC,EAAC,SAC5B;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACN,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,gBAAgB,MAAKC,SAAS,iBACjC/D,OAAA;UAAMQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,iBAAiB;UAAA6B,QAAA,GAC/CxC,IAAI,CAAC4D,gBAAgB,EAAC,SAC3B;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,YAAY,MAAKD,SAAS,iBAC7B/D,OAAA;UAAMQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAC9CxC,IAAI,CAAC8D,YAAY,EAAC,UACvB;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,YAAY,kBACfzD,OAAA;UAAMQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAC9CxC,IAAI,CAACuD,YAAY,EAAC,OACvB;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,aAAa,IAAG,CAAC,iBACpB1D,OAAA;UAAKQ,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,gBACxC1C,OAAA,CAACJ,OAAO;YAACY,SAAS,EAAC;UAAyB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C9C,OAAA;YAAMQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAA6B,QAAA,EAC5DxC,IAAI,CAACwD;UAAa;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAGA,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,YAAY,KAAIzD,IAAI,CAACyD,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/C5D,OAAA;QAAKQ,SAAS,EAAC,MAAM;QAAAkC,QAAA,eACjB1C,OAAA,CAACF,eAAe;UACZ6D,YAAY,EAAEzD,IAAI,CAACyD,YAAa;UAChCE,UAAU,EAAE,CAAE;UACdvD,IAAI,EAAC,OAAO;UACZD,MAAM,EAAC;QAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN9C,OAAA;MAAKQ,SAAS,EAAC,0BAA0B;MAAAkC,QAAA,gBACrC1C,OAAA;QAAKQ,SAAS,EAAG,aAAYU,MAAM,CAACN,IAAK,IAAGR,aAAa,GAAG,eAAe,GAAG,eAAgB,EAAE;QAAAsC,QAAA,EAC3FxC,IAAI,CAAC+D,KAAK,IAAI/D,IAAI,CAACsD,WAAW,IAAI;MAAC;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACN9C,OAAA;QAAKQ,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;QAAA6B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAACoB,EAAA,GA7SIjE,eAAe;AA+SrB,eAAeA,eAAe;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}