{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Fetching ranking data...');\n        const response = await getAllReportsForRanking();\n        console.log('API Response:', response);\n        if (response.success) {\n          console.log('Setting ranking data:', response.data.length, 'users');\n          setRankingData(response.data);\n        } else {\n          console.log('API failed:', response.message);\n          setError(response.message);\n        }\n      } catch (err) {\n        console.log('Error:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading ranking data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-red-600 mb-4\",\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900 mb-8\",\n        children: \"Student Ranking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this), rankingData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-lg\",\n          children: \"No ranking data available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 bg-gray-50 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: [\"Leaderboard (\", rankingData.length, \" students)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divide-y divide-gray-200\",\n          children: rankingData.slice(0, 10).map((user, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\",\n                  children: [\"#\", index + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.userName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.userSchool\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: [user.totalPoints, \" points\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [user.passedExamsCount, \" exams passed\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 37\n            }, this)]\n          }, user.userId, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"unckz5ZqJNfUhgRcoN9mZ9rKWzc=\");\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "getAllReportsForRanking", "jsxDEV", "_jsxDEV", "Ranking", "_s", "rankingData", "setRankingData", "loading", "setLoading", "error", "setError", "fetchData", "console", "log", "response", "success", "data", "length", "message", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "slice", "map", "user", "index", "userName", "userSchool", "totalPoints", "passedExamsCount", "userId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            try {\r\n                console.log('Fetching ranking data...');\r\n                const response = await getAllReportsForRanking();\r\n                console.log('API Response:', response);\r\n\r\n                if (response.success) {\r\n                    console.log('Setting ranking data:', response.data.length, 'users');\r\n                    setRankingData(response.data);\r\n                } else {\r\n                    console.log('API failed:', response.message);\r\n                    setError(response.message);\r\n                }\r\n            } catch (err) {\r\n                console.log('Error:', err);\r\n                setError(err.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n                    <p className=\"text-gray-600\">Loading ranking data...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <h2 className=\"text-2xl font-bold text-red-600 mb-4\">Error</h2>\r\n                    <p className=\"text-gray-600\">{error}</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50 p-8\">\r\n            <div className=\"max-w-4xl mx-auto\">\r\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Student Ranking</h1>\r\n\r\n                {rankingData.length === 0 ? (\r\n                    <div className=\"text-center py-12\">\r\n                        <p className=\"text-gray-600 text-lg\">No ranking data available</p>\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\r\n                        <div className=\"px-6 py-4 bg-gray-50 border-b\">\r\n                            <h2 className=\"text-lg font-semibold text-gray-900\">\r\n                                Leaderboard ({rankingData.length} students)\r\n                            </h2>\r\n                        </div>\r\n                        <div className=\"divide-y divide-gray-200\">\r\n                            {rankingData.slice(0, 10).map((user, index) => (\r\n                                <div key={user.userId} className=\"px-6 py-4 flex items-center justify-between\">\r\n                                    <div className=\"flex items-center space-x-4\">\r\n                                        <div className=\"flex-shrink-0\">\r\n                                            <span className=\"inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\">\r\n                                                #{index + 1}\r\n                                            </span>\r\n                                        </div>\r\n                                        <div>\r\n                                            <p className=\"text-sm font-medium text-gray-900\">{user.userName}</p>\r\n                                            <p className=\"text-sm text-gray-500\">{user.userSchool}</p>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div className=\"text-right\">\r\n                                        <p className=\"text-sm font-medium text-gray-900\">{user.totalPoints} points</p>\r\n                                        <p className=\"text-sm text-gray-500\">{user.passedExamsCount} exams passed</p>\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,uBAAuB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACZ,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACAC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvC,MAAMC,QAAQ,GAAG,MAAMd,uBAAuB,CAAC,CAAC;QAChDY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAAC;QAEtC,IAAIA,QAAQ,CAACC,OAAO,EAAE;UAClBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAACE,IAAI,CAACC,MAAM,EAAE,OAAO,CAAC;UACnEX,cAAc,CAACQ,QAAQ,CAACE,IAAI,CAAC;QACjC,CAAC,MAAM;UACHJ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAACI,OAAO,CAAC;UAC5CR,QAAQ,CAACI,QAAQ,CAACI,OAAO,CAAC;QAC9B;MACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;QACVP,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEM,GAAG,CAAC;QAC1BT,QAAQ,CAACS,GAAG,CAACD,OAAO,CAAC;MACzB,CAAC,SAAS;QACNV,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAEDG,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAGN,IAAIJ,OAAO,EAAE;IACT,oBACIL,OAAA;MAAKkB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBnB,OAAA;UAAKkB,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGvB,OAAA;UAAGkB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIhB,KAAK,EAAE;IACP,oBACIP,OAAA;MAAKkB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBnB,OAAA;UAAIkB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DvB,OAAA;UAAGkB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEZ;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIvB,OAAA;IAAKkB,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eACxCnB,OAAA;MAAKkB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BnB,OAAA;QAAIkB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEzEpB,WAAW,CAACY,MAAM,KAAK,CAAC,gBACrBf,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAC9BnB,OAAA;UAAGkB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,gBAENvB,OAAA;QAAKkB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1DnB,OAAA;UAAKkB,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAC1CnB,OAAA;YAAIkB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,eACnC,EAAChB,WAAW,CAACY,MAAM,EAAC,YACrC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACpChB,WAAW,CAACqB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtC3B,OAAA;YAAuBkB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAC1EnB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACxCnB,OAAA;gBAAKkB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1BnB,OAAA;kBAAMkB,SAAS,EAAC,4GAA4G;kBAAAC,QAAA,GAAC,GACxH,EAACQ,KAAK,GAAG,CAAC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvB,OAAA;gBAAAmB,QAAA,gBACInB,OAAA;kBAAGkB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEO,IAAI,CAACE;gBAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEvB,OAAA;kBAAGkB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEO,IAAI,CAACG;gBAAU;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBnB,OAAA;gBAAGkB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAEO,IAAI,CAACI,WAAW,EAAC,SAAO;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9EvB,OAAA;gBAAGkB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAEO,IAAI,CAACK,gBAAgB,EAAC,eAAa;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA,GAfAG,IAAI,CAACM,MAAM;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBhB,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACrB,EAAA,CA/FID,OAAO;AAAAgC,EAAA,GAAPhC,OAAO;AAiGb,eAAeA,OAAO;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}