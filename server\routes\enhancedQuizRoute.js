const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const enhancedQuizMarkingService = require("../services/enhancedQuizMarkingService");

// Enhanced quiz scoring endpoint
router.post("/calculate-enhanced-score", authMiddleware, async (req, res) => {
  try {
    const {
      questions,
      selectedOptions,
      gptMap,
      timeSpent,
      totalTimeAllowed,
      user,
      examData
    } = req.body;

    // Validate required parameters
    if (!questions || !selectedOptions || !user || !examData) {
      return res.status(400).send({
        message: "Missing required parameters",
        success: false,
      });
    }

    // Calculate enhanced score
    const result = await enhancedQuizMarkingService.calculateEnhancedScore({
      questions,
      selectedOptions,
      gptMap: gptMap || {},
      timeSpent,
      totalTimeAllowed,
      user,
      examData
    });

    // Update user statistics
    await enhancedQuizMarkingService.updateUserStatistics(user._id, result, examData);

    res.send({
      message: "Enhanced score calculated successfully",
      data: result,
      success: true,
    });
  } catch (error) {
    console.error("Enhanced scoring error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Get user achievements
router.get("/achievements/:userId", authMiddleware, async (req, res) => {
  try {
    const { userId } = req.params;
    const User = require("../models/userModel");
    
    const user = await User.findById(userId).select('achievements totalQuizzesTaken totalPointsEarned averageScore bestStreak currentStreak');
    
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Group achievements by type
    const achievementsByType = {};
    user.achievements.forEach(achievement => {
      if (!achievementsByType[achievement.type]) {
        achievementsByType[achievement.type] = [];
      }
      achievementsByType[achievement.type].push(achievement);
    });

    res.send({
      message: "Achievements fetched successfully",
      data: {
        achievements: user.achievements,
        achievementsByType,
        stats: {
          totalQuizzesTaken: user.totalQuizzesTaken || 0,
          totalPointsEarned: user.totalPointsEarned || 0,
          averageScore: user.averageScore || 0,
          bestStreak: user.bestStreak || 0,
          currentStreak: user.currentStreak || 0
        }
      },
      success: true,
    });
  } catch (error) {
    console.error("Get achievements error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Get user performance analytics
router.get("/analytics/:userId", authMiddleware, async (req, res) => {
  try {
    const { userId } = req.params;
    const { timeframe = '30d' } = req.query; // 7d, 30d, 90d, all
    
    const Report = require("../models/reportModel");
    const User = require("../models/userModel");
    
    // Calculate date filter
    let dateFilter = {};
    if (timeframe !== 'all') {
      const days = parseInt(timeframe.replace('d', ''));
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      dateFilter = { createdAt: { $gte: startDate } };
    }

    // Get user reports
    const reports = await Report.find({ 
      user: userId,
      ...dateFilter
    })
    .populate('exam', 'subject name level class')
    .sort({ createdAt: 1 });

    // Get user data
    const user = await User.findById(userId).select('achievements totalQuizzesTaken totalPointsEarned averageScore bestStreak currentStreak');

    // Calculate analytics
    const analytics = {
      totalQuizzes: reports.length,
      averageScore: 0,
      totalPoints: 0,
      passRate: 0,
      subjectPerformance: {},
      scoreProgression: [],
      difficultyAnalysis: {
        easy: { attempted: 0, passed: 0 },
        medium: { attempted: 0, passed: 0 },
        hard: { attempted: 0, passed: 0 }
      },
      recentAchievements: user.achievements.slice(-5),
      streakData: {
        current: user.currentStreak || 0,
        best: user.bestStreak || 0
      }
    };

    if (reports.length > 0) {
      const scores = reports.map(r => r.result.score || 0);
      const passedQuizzes = reports.filter(r => r.result.verdict === 'Pass').length;
      
      analytics.averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
      analytics.totalPoints = reports.reduce((sum, r) => sum + (r.result.points || 0), 0);
      analytics.passRate = Math.round((passedQuizzes / reports.length) * 100);

      // Subject performance
      reports.forEach(report => {
        const subject = report.exam?.subject || 'Unknown';
        if (!analytics.subjectPerformance[subject]) {
          analytics.subjectPerformance[subject] = {
            attempted: 0,
            passed: 0,
            averageScore: 0,
            totalScore: 0
          };
        }
        
        analytics.subjectPerformance[subject].attempted++;
        analytics.subjectPerformance[subject].totalScore += report.result.score || 0;
        
        if (report.result.verdict === 'Pass') {
          analytics.subjectPerformance[subject].passed++;
        }
      });

      // Calculate average scores for subjects
      Object.keys(analytics.subjectPerformance).forEach(subject => {
        const subjectData = analytics.subjectPerformance[subject];
        subjectData.averageScore = Math.round(subjectData.totalScore / subjectData.attempted);
        subjectData.passRate = Math.round((subjectData.passed / subjectData.attempted) * 100);
      });

      // Score progression (last 10 quizzes)
      analytics.scoreProgression = reports.slice(-10).map((report, index) => ({
        quiz: index + 1,
        score: report.result.score || 0,
        subject: report.exam?.subject || 'Unknown',
        date: report.createdAt
      }));
    }

    res.send({
      message: "Analytics fetched successfully",
      data: analytics,
      success: true,
    });
  } catch (error) {
    console.error("Get analytics error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Get leaderboard with enhanced ranking
router.get("/enhanced-leaderboard", authMiddleware, async (req, res) => {
  try {
    const { level, limit = 50 } = req.query;
    const User = require("../models/userModel");
    
    // Build match conditions
    let matchConditions = {};
    if (level && level !== 'all') {
      matchConditions.level = level;
    }

    const leaderboard = await User.aggregate([
      { $match: matchConditions },
      {
        $project: {
          name: 1,
          school: 1,
          class: 1,
          level: 1,
          profileImage: 1,
          subscriptionStatus: 1,
          totalQuizzesTaken: 1,
          totalPointsEarned: 1,
          averageScore: 1,
          bestStreak: 1,
          currentStreak: 1,
          achievements: 1,
          // Calculate enhanced ranking score
          enhancedRankingScore: {
            $add: [
              { $multiply: ["$totalPointsEarned", 1] }, // Base points
              { $multiply: ["$averageScore", 2] }, // Average score weight
              { $multiply: ["$bestStreak", 5] }, // Streak bonus
              { $multiply: [{ $size: "$achievements" }, 10] }, // Achievement bonus
              {
                $cond: [
                  { $in: ["$subscriptionStatus", ["premium", "active"]] },
                  50, // Premium bonus
                  0
                ]
              }
            ]
          }
        }
      },
      { $sort: { enhancedRankingScore: -1, totalPointsEarned: -1, name: 1 } },
      { $limit: parseInt(limit) }
    ]);

    // Add rank to each user
    const rankedLeaderboard = leaderboard.map((user, index) => ({
      ...user,
      rank: index + 1
    }));

    res.send({
      message: "Enhanced leaderboard fetched successfully",
      data: rankedLeaderboard,
      success: true,
    });
  } catch (error) {
    console.error("Enhanced leaderboard error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

module.exports = router;
