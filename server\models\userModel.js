const mongoose = require("mongoose");
const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    school: {
      type: String,
      required: true,
    },
    level: {
      type: String,
      enum: ["primary", "secondary", "advance", "Primary", "Secondary", "Advance"],
      default: "Primary",
      required: false,
    },
    class: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    phoneNumber: {
      type: String,
      required: true,
      unique: true,
    },
    paymentRequired: {
      type: Boolean,
      required: false,
      default: false,
    },
    // Enhanced subscription tracking
    subscriptionStatus: {
      type: String,
      enum: ["free", "premium", "active", "expired"],
      default: "free",
    },
    subscriptionStartDate: {
      type: Date,
    },
    subscriptionEndDate: {
      type: Date,
    },
    subscriptionPlan: {
      type: String,
      enum: ["basic", "premium", "pro"],
    },
    profileImage: {
      type: String,
    },
    // User statistics for ranking
    totalQuizzesTaken: {
      type: Number,
      default: 0,
    },
    totalPointsEarned: {
      type: Number,
      default: 0,
    },
    averageScore: {
      type: Number,
      default: 0,
    },
    bestStreak: {
      type: Number,
      default: 0,
    },
    currentStreak: {
      type: Number,
      default: 0,
    },
    achievements: [{
      type: {
        type: String,
        enum: ["first_quiz", "perfect_score", "streak_5", "streak_10", "streak_20", "subject_master", "speed_demon", "consistent_learner", "improvement_star"],
      },
      earnedAt: {
        type: Date,
        default: Date.now,
      },
      subject: String, // For subject-specific achievements
      metadata: Object, // Additional achievement data
    }],
    password: {
      type: String,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    }
  },
  {
    timestamps: true,
  }
);

const userModel = mongoose.model("users", userSchema);

module.exports = userModel;